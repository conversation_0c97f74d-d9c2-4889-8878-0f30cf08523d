import {
  collection,
  doc,
  getDocs,
  query,
  where,
  writeBatch,
  increment,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Follow } from '@/types';
import { COLLECTIONS } from '../types/collections';

export const followService = {
  async follow(followerId: string, followingId: string): Promise<void> {
    console.log('🔄 FollowService.follow called:', { followerId, followingId });

    // Prevent self-following
    if (followerId === followingId) {
      throw new Error('Benutzer können sich nicht selbst folgen');
    }

    // Check if already following
    const alreadyFollowing = await this.isFollowing(followerId, followingId);
    if (alreadyFollowing) {
      console.log('⚠️ Already following, skipping');
      return;
    }

    const batch = writeBatch(db);

    // Create follow relationship
    const followRef = doc(collection(db, COLLECTIONS.FOLLOWS));
    batch.set(followRef, {
      followerId,
      followingId,
      createdAt: serverTimestamp()
    });

    // Update user stats
    const followerRef = doc(db, COLLECTIONS.USERS, followerId);
    const followingRef = doc(db, COLLECTIONS.USERS, followingId);

    batch.update(followerRef, { totalFollowing: increment(1) });
    batch.update(followingRef, { totalFollowers: increment(1) });

    await batch.commit();
    console.log('✅ Follow relationship created successfully');
  },

  async unfollow(followerId: string, followingId: string): Promise<void> {
    console.log('🔄 FollowService.unfollow called:', { followerId, followingId });

    const q = query(
      collection(db, COLLECTIONS.FOLLOWS),
      where('followerId', '==', followerId),
      where('followingId', '==', followingId)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const batch = writeBatch(db);

      // Delete follow relationship
      batch.delete(querySnapshot.docs[0].ref);

      // Update user stats
      const followerRef = doc(db, COLLECTIONS.USERS, followerId);
      const followingRef = doc(db, COLLECTIONS.USERS, followingId);

      batch.update(followerRef, { totalFollowing: increment(-1) });
      batch.update(followingRef, { totalFollowers: increment(-1) });

      await batch.commit();
      console.log('✅ Unfollow relationship deleted successfully');
    } else {
      console.log('⚠️ No follow relationship found to delete');
    }
  },

  async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    console.log('🔍 FollowService.isFollowing called:', { followerId, followingId });

    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', followerId),
        where('followingId', '==', followingId)
      );
      const querySnapshot = await getDocs(q);
      const isFollowing = !querySnapshot.empty;

      console.log('🔍 Follow status result:', isFollowing);
      return isFollowing;
    } catch (error) {
      console.error('🚨 Error checking follow status:', error);
      return false;
    }
  },

  async getFollowing(userId: string): Promise<string[]> {
    console.log('🔍 FollowService.getFollowing called for userId:', userId);

    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      const followingIds = querySnapshot.docs.map(doc => {
        const data = doc.data();
        console.log('📋 Follow doc data:', data);
        return data.followingId;
      });

      console.log('🔍 Following IDs found:', followingIds);
      console.log('📊 Total follows found:', followingIds.length);
      return followingIds;
    } catch (error) {
      console.error('🚨 Error getting following list:', error);
      return [];
    }
  },

  async getFollowers(userId: string): Promise<string[]> {
    console.log('🔍 FollowService.getFollowers called for userId:', userId);

    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followingId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      const followerIds = querySnapshot.docs.map(doc => doc.data().followerId);

      console.log('🔍 Follower IDs found:', followerIds);
      return followerIds;
    } catch (error) {
      console.error('🚨 Error getting followers list:', error);
      return [];
    }
  },

  async getFollowingWithDetails(userId: string): Promise<Follow[]> {
    console.log('🔍 FollowService.getFollowingWithDetails called for userId:', userId);

    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followerId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Follow[];
    } catch (error) {
      console.error('🚨 Error getting following details:', error);
      return [];
    }
  },

  async getFollowersWithDetails(userId: string): Promise<Follow[]> {
    console.log('🔍 FollowService.getFollowersWithDetails called for userId:', userId);

    try {
      const q = query(
        collection(db, COLLECTIONS.FOLLOWS),
        where('followingId', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Follow[];
    } catch (error) {
      console.error('🚨 Error getting followers details:', error);
      return [];
    }
  },

  async getFollowStats(userId: string): Promise<{ followers: number; following: number }> {
    try {
      const [followers, following] = await Promise.all([
        this.getFollowers(userId),
        this.getFollowing(userId)
      ]);

      return {
        followers: followers.length,
        following: following.length
      };
    } catch (error) {
      console.error('🚨 Error getting follow stats:', error);
      return { followers: 0, following: 0 };
    }
  }
};
