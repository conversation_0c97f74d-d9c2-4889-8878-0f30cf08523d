import { useState, useEffect } from 'react';
import { ratingService } from '@/services/ratingService';
import { useAuth } from '@/context/AuthContext';

export const useRating = (linkId: string) => {
  const { user } = useAuth();
  const [userRating, setUserRating] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user's current rating for this link
  useEffect(() => {
    const loadUserRating = async () => {
      if (!user || !linkId) return;

      try {
        const rating = await ratingService.getUserRating(linkId, user.id);
        setUserRating(rating?.rating || null);
      } catch (err) {
        console.error('Failed to load user rating:', err);
      }
    };

    loadUserRating();
  }, [linkId, user]);

  const submitRating = async (rating: number) => {
    if (!user || !linkId) {
      setError('<PERSON>e müssen angemeldet sein, um zu bewerten');
      return false;
    }

    if (rating < 1 || rating > 5) {
      setError('Bewertung muss zwischen 1 und 5 Sternen liegen');
      return false;
    }

    try {
      setLoading(true);
      setError(null);

      await ratingService.create(linkId, user.id, rating);
      setUserRating(rating);
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Fehler beim Speichern der Bewertung');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    userRating,
    loading,
    error,
    submitRating
  };
}; 