'use client';

import React, { useState } from 'react';
import { followService } from '@/services/followService';

interface FollowDebugProps {
  currentUserId: string;
  targetUserId: string;
}

export default function FollowDebug({ currentUserId, targetUserId }: FollowDebugProps) {
  const [isFollowing, setIsFollowing] = useState<boolean | null>(null);
  const [followingList, setFollowingList] = useState<string[]>([]);
  const [followersList, setFollowersList] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const checkFollowStatus = async () => {
    setLoading(true);
    try {
      const status = await followService.isFollowing(currentUserId, targetUserId);
      setIsFollowing(status);
      console.log('Follow status:', status);
    } catch (error) {
      console.error('Error checking follow status:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFollowingList = async () => {
    setLoading(true);
    try {
      const following = await followService.getFollowing(currentUserId);
      setFollowingList(following);
      console.log('Following list:', following);
    } catch (error) {
      console.error('Error getting following list:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFollowersList = async () => {
    setLoading(true);
    try {
      const followers = await followService.getFollowers(targetUserId);
      setFollowersList(followers);
      console.log('Followers list:', followers);
    } catch (error) {
      console.error('Error getting followers list:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async () => {
    setLoading(true);
    try {
      await followService.follow(currentUserId, targetUserId);
      console.log('Follow successful');
      // Refresh status
      await checkFollowStatus();
    } catch (error) {
      console.error('Error following:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUnfollow = async () => {
    setLoading(true);
    try {
      await followService.unfollow(currentUserId, targetUserId);
      console.log('Unfollow successful');
      // Refresh status
      await checkFollowStatus();
    } catch (error) {
      console.error('Error unfollowing:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-semibold text-white mb-4">🔧 Follow Debug Panel</h3>
      
      <div className="space-y-2 text-sm text-gray-300 mb-4">
        <p><strong className="text-white">Current User ID:</strong> {currentUserId}</p>
        <p><strong className="text-white">Target User ID:</strong> {targetUserId}</p>
        <p><strong className="text-white">Is Following:</strong> 
          <span className={`ml-2 px-2 py-1 rounded text-xs ${
            isFollowing === null ? 'bg-gray-600 text-gray-300' :
            isFollowing ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
          }`}>
            {isFollowing === null ? 'Not checked' : isFollowing ? 'Yes' : 'No'}
          </span>
        </p>
        <p><strong className="text-white">Following Count:</strong> {followingList.length}</p>
        <p><strong className="text-white">Followers Count:</strong> {followersList.length}</p>
      </div>

      <div className="flex flex-wrap gap-2">
        <button
          onClick={checkFollowStatus}
          disabled={loading}
          className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
        >
          Check Follow Status
        </button>
        
        <button
          onClick={getFollowingList}
          disabled={loading}
          className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
        >
          Get Following List
        </button>
        
        <button
          onClick={getFollowersList}
          disabled={loading}
          className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
        >
          Get Followers List
        </button>
        
        <button
          onClick={handleFollow}
          disabled={loading}
          className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700 disabled:opacity-50"
        >
          Follow
        </button>
        
        <button
          onClick={handleUnfollow}
          disabled={loading}
          className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50"
        >
          Unfollow
        </button>
      </div>

      {followingList.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold text-white">Following:</h4>
          <div className="bg-black border border-gray-600 rounded p-2 mt-2">
            <ul className="text-sm text-green-400 font-mono">
              {followingList.map(id => <li key={id}>• {id}</li>)}
            </ul>
          </div>
        </div>
      )}

      {followersList.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold text-white">Followers:</h4>
          <div className="bg-black border border-gray-600 rounded p-2 mt-2">
            <ul className="text-sm text-green-400 font-mono">
              {followersList.map(id => <li key={id}>• {id}</li>)}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
