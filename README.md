# 🔗 deeplinked

Ein menschenkuratiertes Link-Archiv, in dem Nutzer hochwertige externe Links posten, kategorisieren, bewerten und kommentieren können. Gebaut mit Next.js, TypeScript, Tailwind CSS und Firebase.

## 🌟 Features

### ✅ Aktuell implementiert
- **Benutzer-Authentifizierung**: Registrierung, Login, Profilverwaltung
- **Link-Verwaltung**: Links e<PERSON>reichen, kategorisieren und taggen
- **Kategorien-System**: Dynamische Kategorien mit Slugs
- **Bewertungssystem**: 1-5 Sterne pro Link
- **Suchfunktion**: Links nach Titel, Beschreibung und Tags durchsuchen
- **Responsive Design**: Mobile-optimiert mit Tailwind CSS
- **TypeScript**: Vollständig typisiert für bessere Entwicklererfahrung

### 🚧 In Entwicklung
- **Kommentarsystem**: Diskussionen zu Links
- **Follow-System**: Anderen Benutzern folgen
- **Favoriten**: Links als Favoriten markieren
- **Moderation**: Admin-Panel für Link-Freigabe
- **Benachrichtigungen**: Push-Notifications für Aktivitäten

### 🔮 Geplant
- **PWA-Support**: Als App installierbar
- **Dark Mode**: Dunkles Theme
- **Export-Funktionen**: Links als PDF/JSON exportieren
- **Social Features**: Link-Sharing, Social Login
- **Analytics**: Detaillierte Statistiken

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **Icons**: Heroicons
- **Forms**: React Hook Form + Zod Validation
- **Deployment**: Vercel (empfohlen)

## 📁 Projektstruktur

```
/src
  ├── app/                 # Next.js App Router
  │   ├── page.tsx         # Startseite
  │   ├── categories/      # Kategorien-Übersicht
  │   ├── category/[slug]/ # Kategorie-Detail-Seiten
  │   ├── submit/          # Link einreichen
  │   ├── user/[username]/ # Benutzer-Profile
  │   ├── login/           # Authentifizierung
  │   └── register/
  ├── components/          # Wiederverwendbare UI-Komponenten
  │   ├── UI/              # Basis-Komponenten (LinkCard, RatingStars)
  │   └── Layout/          # Layout-Komponenten (Header, Footer)
  ├── context/             # React Context (AuthContext)
  ├── firebase/            # Firebase-Konfiguration und Services
  ├── hooks/               # Custom React Hooks
  ├── types/               # TypeScript-Definitionen
  └── utils/               # Utility-Funktionen
```

## 🚀 Setup & Installation

### 1. Repository klonen
```bash
git clone https://github.com/yourusername/deeplinked.git
cd deeplinked
```

### 2. Dependencies installieren
```bash
npm install
```

### 3. Firebase-Projekt erstellen
1. Gehe zu [Firebase Console](https://console.firebase.google.com/)
2. Erstelle ein neues Projekt
3. Aktiviere Firestore Database
4. Aktiviere Authentication (Email/Password)
5. Kopiere die Konfigurationsdaten

### 4. Umgebungsvariablen konfigurieren
Erstelle eine `.env.local` Datei im Projektroot:

```env
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key-here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
```

### 5. Firestore-Sicherheitsregeln konfigurieren
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users können nur ihre eigenen Daten bearbeiten
    match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Links können von allen gelesen werden, aber nur von angemeldeten Benutzern erstellt
    match /links/{linkId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.submittedBy || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow delete: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Kategorien können von allen gelesen und von angemeldeten Benutzern erstellt werden
    match /categories/{categoryId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update: if request.auth != null;
    }
    
    // Bewertungen können nur vom Eigentümer bearbeitet werden
    match /ratings/{ratingId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Kommentare können von allen gelesen und von angemeldeten Benutzern erstellt werden
    match /comments/{commentId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Follows und Favorites
    match /follows/{followId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == resource.data.followerId;
    }
    
    match /favorites/{favoriteId} {
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

### 6. Entwicklungsserver starten
```bash
npm run dev
```

Die App ist nun unter `http://localhost:3000` verfügbar.

## 🎨 Design-Prinzipien

- **Minimalistisch**: Klares, ablenkungsfreies Design
- **Benutzerfreundlich**: Intuitive Navigation und Bedienung
- **Responsive**: Optimiert für Desktop, Tablet und Mobile
- **Performance**: Schnelle Ladezeiten durch optimierte Komponenten
- **Accessibility**: WCAG-konforme Implementierung

## 🔧 Entwicklung

### Code-Qualität
- **TypeScript**: Strikte Typisierung für weniger Bugs
- **ESLint**: Code-Stil und Best Practices
- **Clean Code**: Lesbare, wartbare Komponentenstruktur

### Component-Architektur
```typescript
// Beispiel: LinkCard-Komponente
interface LinkCardProps {
  link: LinkWithDetails;
  showCategory?: boolean;
  onRate?: (rating: number) => void;
  className?: string;
}
```

### State Management
- **React Context**: Globaler Auth-State
- **Custom Hooks**: Wiederverwendbare Datenlogik
- **Local State**: Komponenten-spezifische States

## 📊 Datenmodell

### Kern-Entitäten
- **User**: Benutzerprofile mit Stats
- **Link**: Eingereichte Links mit Metadaten
- **Category**: Kategorien mit Slugs
- **Rating**: 1-5 Sterne Bewertungen
- **Comment**: Kommentare mit Moderation
- **Follow**: Benutzer-Folge-Beziehungen
- **Favorite**: Gespeicherte Links

## 🚀 Deployment

### Vercel (empfohlen)
1. Vercel-Account erstellen
2. GitHub-Repository verbinden
3. Umgebungsvariablen in Vercel konfigurieren
4. Automatisches Deployment bei Git-Push

### Alternativen
- **Netlify**: Ähnlich zu Vercel
- **Firebase Hosting**: Direkte Integration
- **Docker**: Containerisierte Deployment

## 🤝 Contributing

1. Fork das Repository
2. Feature-Branch erstellen (`git checkout -b feature/amazing-feature`)
3. Änderungen committen (`git commit -m 'Add amazing feature'`)
4. Branch pushen (`git push origin feature/amazing-feature`)
5. Pull Request erstellen

## 📄 Lizenz

Dieses Projekt steht unter der MIT-Lizenz. Siehe `LICENSE` Datei für Details.

## 🆘 Support

Bei Fragen oder Problemen:
- GitHub Issues für Bug-Reports
- Discussions für Feature-Requests
- E-Mail: <EMAIL>

---

**Gebaut mit ❤️ für die Developer-Community**
