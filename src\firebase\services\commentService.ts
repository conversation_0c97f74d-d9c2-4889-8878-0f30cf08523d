import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '../config';
import { Comment } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';

export const commentService = {
  async create(linkId: string, userId: string, content: string, parentId?: string): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.COMMENTS), {
      linkId,
      userId,
      content,
      parentId: parentId || null,
      isApproved: true, // Auto-approve for now, implement moderation later
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Increment comment count on the link
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      totalComments: increment(1)
    });

    return docRef.id;
  },

  async getByLink(linkId: string): Promise<Comment[]> {
    const q = query(
      collection(db, COLLECTIONS.COMMENTS),
      where('linkId', '==', linkId),
      where('isApproved', '==', true),
      orderBy('createdAt', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Comment>(doc));
  },

  async getByLinkNested(linkId: string): Promise<Comment[]> {
    const comments = await this.getByLink(linkId);
    
    // Group comments by parent-child relationship
    const commentMap = new Map<string, Comment & { replies: Comment[] }>();
    const rootComments: (Comment & { replies: Comment[] })[] = [];

    // First pass: create all comments with empty replies array
    comments.forEach(comment => {
      commentMap.set(comment.id, { ...comment, replies: [] });
    });

    // Second pass: organize into hierarchy
    comments.forEach(comment => {
      const commentWithReplies = commentMap.get(comment.id)!;
      
      if (comment.parentId) {
        // This is a reply, add it to parent's replies
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.replies.push(commentWithReplies);
        }
      } else {
        // This is a root comment
        rootComments.push(commentWithReplies);
      }
    });

    return rootComments as Comment[];
  },

  async getByUser(userId: string): Promise<Comment[]> {
    const q = query(
      collection(db, COLLECTIONS.COMMENTS),
      where('userId', '==', userId),
      where('isApproved', '==', true),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Comment>(doc));
  },

  async approve(commentId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.COMMENTS, commentId), {
      isApproved: true,
      updatedAt: serverTimestamp()
    });
  },

  async update(commentId: string, content: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.COMMENTS, commentId), {
      content,
      editedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  },

  async getPending(): Promise<Comment[]> {
    const q = query(
      collection(db, COLLECTIONS.COMMENTS),
      where('isApproved', '==', false),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Comment>(doc));
  },

  async getRecentComments(limitCount: number = 10): Promise<Comment[]> {
    const q = query(
      collection(db, COLLECTIONS.COMMENTS),
      where('isApproved', '==', true),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs
      .slice(0, limitCount)
      .map(doc => serializeDoc<Comment>(doc));
  }
}; 