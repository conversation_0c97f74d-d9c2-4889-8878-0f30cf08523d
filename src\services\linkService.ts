import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { Link, LinkWithDetails } from '@/types';
import { COLLECTIONS } from '@/firebase/types/collections';
import { serializeDoc } from '@/firebase/utils/firestore-helpers';
import { categoryService } from '@/services/categoryService';
import { userService } from '@/services/userService';

export const linkService = {
  async create(linkData: Omit<Link, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    console.log('🔗 LinkService.create called with data:', linkData);
    
    try {
      const docRef = await addDoc(collection(db, COLLECTIONS.LINKS), {
        ...linkData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalRatings: 0,
        averageRating: 0,
        totalFavorites: 0
      });

      // Update user's total links count
      if (linkData.userId) {
        const userRef = doc(db, COLLECTIONS.USERS, linkData.userId);
        await updateDoc(userRef, {
          totalLinks: increment(1)
        });
      }

      console.log('✅ Link created successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('🚨 Error creating link:', error);
      throw error;
    }
  },

  async getById(id: string): Promise<Link | null> {
    console.log('🔗 LinkService.getById called with id:', id);
    
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.LINKS, id));
      if (docSnap.exists()) {
        const linkData = serializeDoc<Link>(docSnap);
        console.log('🔗 Link data loaded:', linkData);
        return linkData;
      } else {
        console.log('🔗 No link found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in linkService.getById:', error);
      return null;
    }
  },

  async update(id: string, data: Partial<Link>): Promise<void> {
    console.log('🔗 LinkService.update called:', { id, data });
    
    try {
      await updateDoc(doc(db, COLLECTIONS.LINKS, id), {
        ...data,
        updatedAt: serverTimestamp()
      });
      console.log('✅ Link updated successfully');
    } catch (error) {
      console.error('🚨 Error updating link:', error);
      throw error;
    }
  },

  async delete(id: string): Promise<void> {
    console.log('🔗 LinkService.delete called with id:', id);
    
    try {
      // Get link data first to update user stats
      const link = await this.getById(id);
      
      await deleteDoc(doc(db, COLLECTIONS.LINKS, id));
      
      // Update user's total links count
      if (link?.userId) {
        const userRef = doc(db, COLLECTIONS.USERS, link.userId);
        await updateDoc(userRef, {
          totalLinks: increment(-1)
        });
      }
      
      console.log('✅ Link deleted successfully');
    } catch (error) {
      console.error('🚨 Error deleting link:', error);
      throw error;
    }
  },

  async getByUser(userId: string, limitCount: number = 20): Promise<Link[]> {
    console.log('🔗 LinkService.getByUser called:', { userId, limitCount });
    
    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      
      console.log('🔗 Found', links.length, 'links for user');
      return links;
    } catch (error) {
      console.error('🚨 Error getting user links:', error);
      return [];
    }
  },

  async getByCategory(categoryId: string, limitCount: number = 20): Promise<LinkWithDetails[]> {
    console.log('🔗 LinkService.getByCategory called:', { categoryId, limitCount });

    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('categoryId', '==', categoryId),
        where('isApproved', '==', true),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

      console.log('🔗 Found', links.length, 'links for category');
      return this.enrichLinksWithDetails(links);
    } catch (error) {
      console.error('🚨 Error getting category links:', error);
      return [];
    }
  },

  async getAll(options: {
    pageSize?: number;
    sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
  } = {}): Promise<{ data: LinkWithDetails[] }> {
    const { pageSize = 50, sortBy = 'newest' } = options;

    console.log('🔗 LinkService.getAll called:', { pageSize, sortBy });

    try {
      let q = query(
        collection(db, COLLECTIONS.LINKS),
        where('isApproved', '==', true),
        limit(pageSize)
      );

      // Apply sorting
      if (sortBy === 'rating') {
        q = query(q, orderBy('averageRating', 'desc'), orderBy('totalRatings', 'desc'));
      } else if (sortBy === 'popular') {
        q = query(q, orderBy('totalRatings', 'desc'), orderBy('totalComments', 'desc'));
      } else if (sortBy === 'oldest') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }

      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      const enrichedLinks = await this.enrichLinksWithDetails(links);

      console.log('🔗 Found', enrichedLinks.length, 'links');
      return { data: enrichedLinks };
    } catch (error) {
      console.error('🚨 Error getting all links:', error);
      return { data: [] };
    }
  },

  async getPending(limitCount: number = 20): Promise<LinkWithDetails[]> {
    console.log('🔗 LinkService.getPending called with limit:', limitCount);

    try {
      const q = query(
        collection(db, COLLECTIONS.LINKS),
        where('isApproved', '==', false),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

      console.log('🔗 Found', links.length, 'pending links');
      return this.enrichLinksWithDetails(links);
    } catch (error) {
      console.error('🚨 Error getting pending links:', error);
      return [];
    }
  },

  // Helper Functions
  async enrichLinksWithDetails(links: Link[]): Promise<LinkWithDetails[]> {
    return Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        // Use placeholder category if missing
        const effectiveCategory = category || {
          id: 'unknown',
          name: 'Unbekannte Kategorie',
          slug: 'unknown',
          description: 'Kategorie nicht gefunden',
          isActive: false,
          createdBy: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          level: 0,
          path: 'unknown'
        };

        // Use placeholder submitter if missing
        const effectiveSubmitter = submitter || {
          id: 'unknown',
          username: 'unknown-user',
          displayName: 'Unbekannter Benutzer',
          email: '<EMAIL>',
          avatar: undefined,
          bio: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          totalFollowers: 0,
          totalFollowing: 0
        };

        return {
          ...link,
          category: effectiveCategory,
          submitter: {
            id: effectiveSubmitter.id,
            username: effectiveSubmitter.username,
            displayName: effectiveSubmitter.displayName,
            avatar: effectiveSubmitter.avatar
          }
        } as LinkWithDetails;
      })
    );
  },

  async getTotalCount(): Promise<number> {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.LINKS));
      return querySnapshot.size;
    } catch (error) {
      console.error('🚨 Error getting link count:', error);
      return 0;
    }
  },

  // Admin Functions
  async approve(linkId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isApproved: true,
      approvedAt: serverTimestamp()
    });
  },

  async reject(linkId: string): Promise<void> {
    // For now, we'll delete rejected links. In production, you might want to keep them with a rejected status
    await deleteDoc(doc(db, COLLECTIONS.LINKS, linkId));
  },

  async feature(linkId: string, featured: boolean): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isFeatured: featured
    });
  },

  async getTotalApprovedCount(): Promise<number> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  }
};
