'use client';

import React, { useState } from 'react';
import { auth } from '@/firebase/config';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';

const FirebaseTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('test123');
  const [result, setResult] = useState<string>('');

  const testSignIn = async () => {
    try {
      setResult('Testing sign in...');
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      setResult(`✅ Sign in successful: ${userCredential.user.email}`);
    } catch (error: unknown) {
      const firebaseError = error as { code?: string; message?: string };
      setResult(`❌ Sign in failed: ${firebaseError.code} - ${firebaseError.message}`);
    }
  };

  const testSignUp = async () => {
    try {
      setResult('Testing sign up...');
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      setResult(`✅ Sign up successful: ${userCredential.user.email}`);
    } catch (error: unknown) {
      const firebaseError = error as { code?: string; message?: string };
      setResult(`❌ Sign up failed: ${firebaseError.code} - ${firebaseError.message}`);
    }
  };

  const testConfig = () => {
    const config = {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Missing',
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? 'Set' : 'Missing',
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? 'Set' : 'Missing',
    };
    setResult(`Firebase Config: ${JSON.stringify(config, null, 2)}`);
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">🔥 Firebase Test</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Email:</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Password:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          />
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={testConfig}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Test Config
          </button>
          <button
            onClick={testSignUp}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Test Sign Up
          </button>
          <button
            onClick={testSignIn}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Test Sign In
          </button>
        </div>
        
        {result && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <pre className="text-sm whitespace-pre-wrap">{result}</pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default FirebaseTest;
