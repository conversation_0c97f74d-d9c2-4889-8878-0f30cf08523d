'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import {
  HomeIcon,
  LinkIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  LinkIcon as LinkIconSolid,
  FolderIcon as FolderIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  PlusIcon as PlusIconSolid,
  UserIcon as UserIconSolid
} from '@heroicons/react/24/solid';

const MobileBottomNav: React.FC = () => {
  const { user } = useAuth();
  const pathname = usePathname();

  const isActiveRoute = (route: string) => {
    if (route === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(route);
  };

  const navItems = [
    {
      name: 'Home',
      href: '/',
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
    },
    {
      name: 'Links',
      href: '/links',
      icon: LinkIcon,
      iconSolid: LinkIconSolid,
    },
    {
      name: 'Suche',
      href: '/search',
      icon: MagnifyingGlassIcon,
      iconSolid: MagnifyingGlassIconSolid,
    },
    {
      name: 'Kategorien',
      href: '/categories',
      icon: FolderIcon,
      iconSolid: FolderIconSolid,
    },
    ...(user ? [
      {
        name: 'Neu',
        href: '/submit',
        icon: PlusIcon,
        iconSolid: PlusIconSolid,
      }
    ] : [
      {
        name: 'Login',
        href: '/login',
        icon: UserIcon,
        iconSolid: UserIconSolid,
      }
    ])
  ];

  return (
    <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
      <div className="grid grid-cols-5 h-16">
        {navItems.map((item) => {
          const isActive = isActiveRoute(item.href);
          const Icon = isActive ? item.iconSolid : item.icon;
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`flex flex-col items-center justify-center space-y-1 transition-colors ${
                isActive
                  ? 'text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default MobileBottomNav;
