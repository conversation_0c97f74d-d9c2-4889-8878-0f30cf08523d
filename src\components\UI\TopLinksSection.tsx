'use client';

import React from 'react';
import Link from 'next/link';
import { useTopLinksQuery } from '@/hooks/queries/useTopLinksQuery';
import LinkCard from '@/components/UI/LinkCard';
import { FireIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

interface TopLinksSectionProps {
  timeframe?: 'week' | 'month' | 'all';
  limit?: number;
  className?: string;
}

const TopLinksSection: React.FC<TopLinksSectionProps> = ({ 
  timeframe = 'week', 
  limit = 6,
  className = ""
}) => {
  const { data: topLinks = [], isLoading, error } = useTopLinksQuery(limit, timeframe);

  const getTimeframeText = () => {
    switch (timeframe) {
      case 'week': return 'dieser Woche';
      case 'month': return 'dieses Monats';
      case 'all': return 'aller Zeiten';
      default: return 'dieser Woche';
    }
  };

  if (isLoading) {
    return (
      <section className={`py-16 bg-white ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-48 mx-auto"></div>
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-lg h-48"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error || topLinks.length === 0) {
    return null; // Don't show section if no data or error
  }

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <FireIcon className="h-8 w-8 text-orange-500" />
            <h2 className="text-3xl font-bold text-gray-900">
              Top bewertete Links {getTimeframeText()}
            </h2>
          </div>
          <p className="mt-4 text-lg text-gray-600">
            Die besten Empfehlungen der Community
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {topLinks.slice(0, 4).map((link) => (
            <LinkCard
              key={link.id}
              link={link}
              showCategory={true}
              className="border-orange-100 hover:border-orange-200"
            />
          ))}
        </div>

        {topLinks.length > 4 && (
          <div className="text-center mt-8">
            <Link
              href={`/links?sort=rating&timeframe=${timeframe}`}
              className="inline-flex items-center text-orange-600 hover:text-orange-700 font-medium"
            >
              Alle top bewerteten Links anzeigen
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default TopLinksSection;
