'use client';

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { migrateCategoriesHierarchy } from '@/utils/migrateCategoriesHierarchy';
import { 
  ExclamationTriangleIcon, 
  CheckCircleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline';

export default function MigratePage() {
  const { user, userIsAdmin } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  if (!user || !userIsAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Zugriff verweigert</h2>
          <p className="text-gray-600">Diese Seite ist nur für Administratoren zugänglich.</p>
        </div>
      </div>
    );
  }

  const runMigration = async () => {
    setIsRunning(true);
    setResult(null);

    try {
      await migrateCategoriesHierarchy();
      setResult({
        success: true,
        message: 'Migration erfolgreich abgeschlossen! Alle Kategorien wurden für die hierarchische Struktur aktualisiert.'
      });
    } catch (error) {
      setResult({
        success: false,
        message: `Migration fehlgeschlagen: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Kategorie-Hierarchie Migration</h1>
            <p className="mt-2 text-gray-600">
              Aktualisiert bestehende Kategorien für die neue hierarchische Struktur
            </p>
          </div>

          <div className="p-6">
            {/* Warning */}
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mt-0.5" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Wichtiger Hinweis
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Diese Migration fügt hierarchische Felder zu bestehenden Kategorien hinzu</li>
                      <li>Alle bestehenden Kategorien werden zu Hauptkategorien (Level 0)</li>
                      <li>Die Migration ist sicher und kann mehrfach ausgeführt werden</li>
                      <li>Bereits migrierte Kategorien werden übersprungen</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Migration Button */}
            <div className="mb-6">
              <button
                onClick={runMigration}
                disabled={isRunning}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isRunning ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Migration läuft...
                  </>
                ) : (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    Migration starten
                  </>
                )}
              </button>
            </div>

            {/* Result */}
            {result && (
              <div className={`p-4 rounded-md ${
                result.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex">
                  {result.success ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-400 mt-0.5" />
                  ) : (
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mt-0.5" />
                  )}
                  <div className="ml-3">
                    <h3 className={`text-sm font-medium ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.success ? 'Migration erfolgreich' : 'Migration fehlgeschlagen'}
                    </h3>
                    <p className={`mt-2 text-sm ${
                      result.success ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {result.message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* What this migration does */}
            <div className="mt-8 border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Was diese Migration macht:
              </h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="ml-3">
                    Fügt <code className="bg-gray-100 px-1 rounded">level: 0</code> zu allen bestehenden Kategorien hinzu
                  </p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="ml-3">
                    Setzt <code className="bg-gray-100 px-1 rounded">parentId: null</code> für Hauptkategorien
                  </p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="ml-3">
                    Erstellt <code className="bg-gray-100 px-1 rounded">path</code> basierend auf dem Kategorie-Slug
                  </p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="ml-3">
                    Initialisiert <code className="bg-gray-100 px-1 rounded">totalSubcategories: 0</code>
                  </p>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                  <p className="ml-3">
                    Generiert fehlende Slugs automatisch
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
