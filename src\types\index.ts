// Core Types for deeplinked App
// Designed for scalability and type safety

export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  bio?: string;
  avatar?: string;
  website?: string;
  isVerified?: boolean;
  isAdmin?: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Stats (computed fields)
  totalLinks?: number;
  totalFollowers?: number;
  totalFollowing?: number;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string; // For visual categorization
  icon?: string; // Icon name for UI
  isActive: boolean;
  createdBy: string; // User ID
  createdAt: Date;
  updatedAt: Date;
  // Hierarchical structure
  parentId?: string; // ID of parent category (null for root categories)
  level: number; // 0 for root, 1 for subcategory, etc.
  path: string; // Full path like "programming/javascript" for breadcrumbs
  // Stats
  totalLinks?: number;
  totalSubcategories?: number;
}

export interface Link {
  id: string;
  title: string;
  url: string;
  description?: string;
  domain: string; // Extracted from URL for filtering
  categoryId: string;
  submittedBy: string; // User ID
  isApproved: boolean; // For moderation
  isFeatured?: boolean; // Admin can feature important links
  tags?: string[]; // Additional tagging system
  submittedAt: Date;
  approvedAt?: Date;
  updatedAt: Date;
  // Computed stats
  averageRating?: number;
  totalRatings?: number;
  totalComments?: number;
}

export interface Rating {
  id: string;
  linkId: string;
  userId: string;
  rating: number; // 1-5 stars
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  linkId: string;
  userId: string;
  content: string;
  parentId?: string; // For nested comments (future feature)
  isApproved: boolean; // Moderation
  createdAt: Date;
  updatedAt: Date;
  // Additional metadata
  editedAt?: Date;
  likes?: number; // Future: like system for comments
  // Author information (loaded separately)
  author?: {
    id: string;
    username: string;
    displayName: string;
    avatar?: string;
  };
}

export interface Follow {
  id: string;
  followerId: string; // User who follows
  followingId: string; // User being followed
  createdAt: Date;
}

export interface Favorite {
  id: string;
  userId: string;
  linkId: string;
  createdAt: Date;
}

// Form Types for input validation
export interface CreateLinkForm {
  title: string;
  url: string;
  description?: string;
  categoryId: string;
  newCategoryName?: string; // If user wants to create new category
  tags?: string[];
}

export interface CreateCategoryForm {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  parentId?: string; // For creating subcategories
}

export interface UserProfileForm {
  displayName: string;
  bio?: string;
  website?: string;
}

export interface CommentForm {
  content: string;
  parentId?: string;
}

// API Response Types
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface LinkWithDetails extends Link {
  category: Category;
  submitter: Pick<User, 'id' | 'username' | 'displayName' | 'avatar'>;
  userRating?: number; // Current user's rating
  isFavorited?: boolean; // Is favorited by current user
}

export interface CategoryWithStats extends Category {
  recentLinks?: LinkWithDetails[];
  subcategories?: Category[];
  parentCategory?: Category;
}

export interface UserWithStats extends User {
  recentLinks?: LinkWithDetails[];
  isFollowing?: boolean; // Is followed by current user
}

// UI State Types
export interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface FilterOptions {
  category?: string;
  sortBy: 'newest' | 'oldest' | 'rating' | 'popular';
  timeRange: 'day' | 'week' | 'month' | 'year' | 'all';
  domain?: string;
  tags?: string[];
}

// Constants for validation
export const RATING_RANGE = {
  MIN: 1,
  MAX: 5
} as const;

export const VALIDATION_LIMITS = {
  LINK_TITLE_MAX: 200,
  LINK_DESCRIPTION_MAX: 500,
  CATEGORY_NAME_MAX: 50,
  CATEGORY_DESCRIPTION_MAX: 200,
  COMMENT_MAX: 1000,
  BIO_MAX: 300,
  USERNAME_MIN: 3,
  USERNAME_MAX: 30
} as const;

// Error types for better error handling
export interface AppError {
  code: string;
  message: string;
  field?: string;
}

// Category with statistics
export interface CategoryWithStats extends Category {
  linkCount: number;
  totalRating: number;
  averageRating: number;
  subcategories?: CategoryWithStats[];
}

export type SubmissionStatus = 'draft' | 'pending' | 'approved' | 'rejected';
export type UserRole = 'user' | 'moderator' | 'admin';
export type NotificationType = 'follow' | 'comment' | 'rating' | 'feature';

export interface Report {
  id: string;
  reportedBy: string; // User ID who reported
  targetType: 'link' | 'comment'; // What is being reported
  targetId: string; // ID of the link or comment
  reason: 'spam' | 'inappropriate' | 'harassment' | 'copyright' | 'misinformation' | 'other';
  customReason?: string; // Additional details if reason is 'other'
  description?: string; // Optional description
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  createdAt: Date;
  reviewedAt?: Date;
  reviewedBy?: string; // Admin who reviewed
  reviewNotes?: string; // Admin notes
} 