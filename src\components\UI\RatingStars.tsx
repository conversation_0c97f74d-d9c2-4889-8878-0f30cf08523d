import React, { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/solid';
import { StarIcon as StarIconOutline } from '@heroicons/react/24/outline';

interface RatingStarsProps {
  rating?: number;
  totalRatings?: number;
  userRating?: number | null;
  onRate?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  className?: string;
}

export default function RatingStars({
  rating = 0,
  totalRatings = 0,
  userRating,
  onRate,
  readonly = false,
  size = 'md',
  showCount = true,
  className = ''
}: RatingStarsProps) {
  const [hoverRating, setHoverRating] = useState<number | null>(null);

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const iconSize = sizeClasses[size];
  const textSize = textSizeClasses[size];

  const displayRating = hoverRating || userRating || rating;
  const isInteractive = !readonly && onRate;

  const handleStarClick = (starRating: number) => {
    if (isInteractive) {
      onRate!(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (isInteractive) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (isInteractive) {
      setHoverRating(null);
    }
  };

  // Format rating to 1 decimal place
  const formattedRating = Number(displayRating).toFixed(1);

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {/* Stars */}
      <div 
        className="flex space-x-0.5" 
        onMouseLeave={handleMouseLeave}
      >
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= (hoverRating || displayRating || 0);
          const isHalfFilled = !Number.isInteger(displayRating || 0) && 
                               star === Math.ceil(displayRating || 0) && 
                               star > (displayRating || 0);

          return (
            <button
              key={star}
              type="button"
              disabled={!isInteractive}
              className={`${
                isInteractive 
                  ? 'cursor-pointer hover:scale-110 transition-transform' 
                  : 'cursor-default'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-sm`}
              onClick={() => handleStarClick(star)}
              onMouseEnter={() => handleStarHover(star)}
              aria-label={`${star} von 5 Sternen bewerten`}
            >
              {isFilled ? (
                <StarIcon 
                  className={`${iconSize} ${
                    userRating === star ? 'text-yellow-500' : 
                    hoverRating === star ? 'text-yellow-400' : 
                    'text-yellow-400'
                  }`} 
                />
              ) : isHalfFilled ? (
                <div className="relative">
                  <StarIconOutline className={`${iconSize} text-gray-300`} />
                  <div 
                    className="absolute inset-0 overflow-hidden" 
                    style={{ width: `${(displayRating! % 1) * 100}%` }}
                  >
                    <StarIcon className={`${iconSize} text-yellow-400`} />
                  </div>
                </div>
              ) : (
                <StarIconOutline 
                  className={`${iconSize} ${
                    isInteractive && hoverRating ? 'text-gray-400' : 'text-gray-300'
                  }`} 
                />
              )}
            </button>
          );
        })}
      </div>

      {/* Rating Text */}
      {showCount && (displayRating > 0 || totalRatings > 0) && (
        <div className={`${textSize} text-gray-600 flex items-center space-x-1`}>
          {displayRating > 0 && (
            <span className="font-medium">{formattedRating}</span>
          )}
          {totalRatings > 0 && (
            <span className="text-gray-500">
              ({totalRatings} {totalRatings === 1 ? 'Bewertung' : 'Bewertungen'})
            </span>
          )}
        </div>
      )}

      {/* User's Rating Indicator */}
      {userRating && !hoverRating && (
        <span className={`${textSize} text-blue-600 font-medium`}>
          Ihre Bewertung: {userRating}
        </span>
      )}

      {/* Hover Rating Preview */}
      {isInteractive && hoverRating && (
        <span className={`${textSize} text-gray-500`}>
          {hoverRating} von 5 Sternen
        </span>
      )}
    </div>
  );
} 