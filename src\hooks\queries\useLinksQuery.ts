import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { linkService } from '@/services/linkService';
import { LinkWithDetails, FilterOptions } from '@/types';

// Query Keys
export const linkKeys = {
  all: ['links'] as const,
  lists: () => [...linkKeys.all, 'list'] as const,
  list: (filters: FilterOptions) => [...linkKeys.lists(), filters] as const,
  details: () => [...linkKeys.all, 'detail'] as const,
  detail: (id: string) => [...linkKeys.details(), id] as const,
  recent: (limit: number) => [...linkKeys.all, 'recent', limit] as const,
  pending: () => [...linkKeys.all, 'pending'] as const,
  byCategory: (categoryId: string) => [...linkKeys.all, 'category', categoryId] as const,
  search: (query: string, filters?: Record<string, unknown>) => [...linkKeys.all, 'search', query, filters] as const,
};

// Recent Links Query
export const useRecentLinksQuery = (limit: number = 6) => {
  return useQuery({
    queryKey: linkKeys.recent(limit),
    queryFn: () => linkService.getRecentApproved(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Links with Filters Query
export const useLinksQuery = (filters: FilterOptions, pageSize: number = 20) => {
  return useQuery({
    queryKey: linkKeys.list(filters),
    queryFn: () => linkService.getAll({ ...filters, pageSize }),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Infinite Links Query (for pagination)
export const useInfiniteLinksQuery = (filters: FilterOptions, pageSize: number = 20) => {
  return useInfiniteQuery({
    queryKey: linkKeys.list(filters),
    queryFn: ({ pageParam }) => linkService.getAll({ 
      ...filters, 
      pageSize,
      lastDoc: pageParam 
    }),
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.hasNext ? lastPage.data[lastPage.data.length - 1] : undefined;
    },
    initialPageParam: undefined,
    staleTime: 1 * 60 * 1000,
  });
};

// Single Link Query
export const useLinkQuery = (linkId: string) => {
  return useQuery({
    queryKey: linkKeys.detail(linkId),
    queryFn: () => linkService.getById(linkId),
    enabled: !!linkId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Pending Links Query (Admin)
export const usePendingLinksQuery = () => {
  return useQuery({
    queryKey: linkKeys.pending(),
    queryFn: () => linkService.getPending(),
    staleTime: 30 * 1000, // 30 seconds (more frequent updates for admin)
  });
};

// Category Links Query
export const useCategoryLinksQuery = (categoryId: string, pageSize: number = 20) => {
  return useQuery({
    queryKey: linkKeys.byCategory(categoryId),
    queryFn: () => linkService.getByCategory(categoryId, pageSize),
    enabled: !!categoryId,
    staleTime: 2 * 60 * 1000,
  });
};

// Search Query
export const useSearchQuery = (query: string, filters?: Record<string, unknown>) => {
  return useQuery({
    queryKey: linkKeys.search(query, filters),
    queryFn: () => linkService.search(query, filters),
    enabled: !!query.trim(),
    staleTime: 1 * 60 * 1000,
  });
};

// Mutations
export const useLinkMutations = () => {
  const queryClient = useQueryClient();

  const approveMutation = useMutation({
    mutationFn: (linkId: string) => linkService.approve(linkId),
    onSuccess: (_, linkId) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: linkKeys.pending() });
      queryClient.invalidateQueries({ queryKey: linkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: linkKeys.detail(linkId) });
    },
  });

  const rejectMutation = useMutation({
    mutationFn: (linkId: string) => linkService.reject(linkId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: linkKeys.pending() });
      queryClient.invalidateQueries({ queryKey: linkKeys.lists() });
    },
  });

  const featureMutation = useMutation({
    mutationFn: ({ linkId, featured }: { linkId: string; featured: boolean }) => 
      linkService.feature(linkId, featured),
    onSuccess: (_, { linkId }) => {
      queryClient.invalidateQueries({ queryKey: linkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: linkKeys.detail(linkId) });
    },
  });

  const rateMutation = useMutation({
    mutationFn: ({ linkId, rating, userId }: { linkId: string; rating: number; userId: string }) => 
      linkService.rate(linkId, rating, userId),
    onSuccess: (_, { linkId }) => {
      queryClient.invalidateQueries({ queryKey: linkKeys.detail(linkId) });
      queryClient.invalidateQueries({ queryKey: linkKeys.lists() });
    },
  });

  const submitMutation = useMutation({
    mutationFn: (linkData: Record<string, unknown>) => linkService.create(linkData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: linkKeys.pending() });
      queryClient.invalidateQueries({ queryKey: linkKeys.lists() });
    },
  });

  return {
    approve: approveMutation,
    reject: rejectMutation,
    feature: featureMutation,
    rate: rateMutation,
    submit: submitMutation,
  };
};

// Optimistic Updates Helper
export const useOptimisticLinkUpdate = () => {
  const queryClient = useQueryClient();

  const updateLinkOptimistically = (linkId: string, updates: Partial<LinkWithDetails>) => {
    // Update all relevant queries optimistically
    queryClient.setQueriesData(
      { queryKey: linkKeys.lists() },
      (oldData: unknown) => {
        if (!oldData) return oldData;
        
        if (oldData.data) {
          // Handle paginated response
          return {
            ...oldData,
            data: oldData.data.map((link: LinkWithDetails) =>
              link.id === linkId ? { ...link, ...updates } : link
            ),
          };
        }
        
        // Handle array response
        return oldData.map((link: LinkWithDetails) =>
          link.id === linkId ? { ...link, ...updates } : link
        );
      }
    );

    // Update single link query
    queryClient.setQueryData(
      linkKeys.detail(linkId),
      (oldData: LinkWithDetails | undefined) => 
        oldData ? { ...oldData, ...updates } : oldData
    );
  };

  return { updateLinkOptimistically };
};
