'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useCategories } from '@/hooks/useCategories';
import { linkService } from '@/services/linkService';
import { categoryService } from '@/services/categoryService';
import { CreateLinkForm, CreateCategoryForm } from '@/types';
import {
  LinkIcon,
  TagIcon,
  FolderIcon,
  PlusIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { isYouTubeUrl } from '@/utils/domains';

export default function SubmitPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { categories, loading: categoriesLoading } = useCategories();
  
  const [formData, setFormData] = useState<CreateLinkForm>({
    title: '',
    url: '',
    description: '',
    categoryId: '',
    newCategoryName: '',
    tags: []
  });
  
  const [newCategoryData, setNewCategoryData] = useState<CreateCategoryForm>({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: 'GlobeAltIcon'
  });

  const [showNewCategory, setShowNewCategory] = useState(false);
  const [currentTag, setCurrentTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  if (!user) {
    router.push('/login');
    return null;
  }

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleAddTag = () => {
    if (currentTag.trim() && !formData.tags?.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), currentTag.trim()]
      }));
      setCurrentTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      // Validation
      if (!formData.title.trim()) {
        throw new Error('Titel ist erforderlich');
      }
      if (!formData.url.trim()) {
        throw new Error('URL ist erforderlich');
      }
      if (!validateUrl(formData.url)) {
        throw new Error('Bitte gib eine gültige URL ein');
      }
      if (!formData.categoryId && !formData.newCategoryName?.trim()) {
        throw new Error('Bitte wähle eine Kategorie oder erstelle eine neue');
      }

      let categoryId = formData.categoryId;

      // Create new category if needed
      if (!categoryId && formData.newCategoryName?.trim()) {
        categoryId = await categoryService.create(
          {
            ...newCategoryData,
            name: formData.newCategoryName
          },
          user.id
        );
      }

      // Create the link
      await linkService.create(
        {
          ...formData,
          categoryId: categoryId!
        },
        user.id
      );

      // Check if this was auto-approved and redirect accordingly
      const isAutoApproved = isYouTubeUrl(formData.url);
      if (isAutoApproved) {
        router.push(`/categories?submitted=true&autoApproved=true`);
      } else {
        router.push(`/categories?submitted=true&autoApproved=false`);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ein Fehler ist aufgetreten');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Zurück zur Startseite
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Link einreichen</h1>
          <p className="mt-2 text-gray-600">
            Teile einen hochwertigen Link mit der Community. YouTube-Links werden automatisch genehmigt, andere Links werden vor der Veröffentlichung geprüft.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-sm rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            
            {/* URL Field */}
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
                URL *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LinkIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="url"
                  id="url"
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                  placeholder="https://example.com/awesome-resource"
                  value={formData.url}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                />
              </div>

              {/* YouTube Auto-Approval Notice */}
              {formData.url && isYouTubeUrl(formData.url) && (
                <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-green-800">
                        <strong>YouTube-Link erkannt!</strong> Dieser Link wird automatisch genehmigt und ist sofort nach dem Einreichen sichtbar.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Title Field */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Titel *
              </label>
              <input
                type="text"
                id="title"
                required
                maxLength={200}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                placeholder="Kurzer, aussagekräftiger Titel"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
              <p className="mt-1 text-sm text-gray-500">
                {formData.title.length}/200 Zeichen
              </p>
            </div>

            {/* Description Field */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Beschreibung
              </label>
              <textarea
                id="description"
                rows={4}
                maxLength={500}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                placeholder="Was macht diesen Link besonders? Warum sollten andere ihn besuchen?"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
              <p className="mt-1 text-sm text-gray-500">
                {formData.description?.length || 0}/500 Zeichen
              </p>
            </div>

            {/* Category Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kategorie *
              </label>
              
              {!showNewCategory ? (
                <div className="space-y-3">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FolderIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <select
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                      value={formData.categoryId}
                      onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
                      disabled={categoriesLoading}
                    >
                      <option value="">Kategorie auswählen...</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {'  '.repeat(category.level)}{category.name} ({category.totalLinks || 0} Links)
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <button
                    type="button"
                    onClick={() => setShowNewCategory(true)}
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Neue Kategorie erstellen
                  </button>
                </div>
              ) : (
                <div className="space-y-3 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">Neue Kategorie erstellen</h3>
                    <button
                      type="button"
                      onClick={() => {
                        setShowNewCategory(false);
                        setFormData(prev => ({ ...prev, newCategoryName: '' }));
                      }}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Abbrechen
                    </button>
                  </div>
                  
                  <input
                    type="text"
                    placeholder="Name der neuen Kategorie"
                    maxLength={50}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    value={formData.newCategoryName}
                    onChange={(e) => setFormData(prev => ({ ...prev, newCategoryName: e.target.value }))}
                  />
                  
                  <input
                    type="text"
                    placeholder="Beschreibung (optional)"
                    maxLength={200}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    value={newCategoryData.description}
                    onChange={(e) => setNewCategoryData(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
              )}
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags (optional)
              </label>
              
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags?.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <TagIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Tag hinzufügen..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                </div>
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Hinzufügen
                </button>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <Link
                href="/"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Abbrechen
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Wird eingereicht...' : 'Link einreichen'}
              </button>
            </div>
            
          </form>
        </div>

        {/* Info Box */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Qualitätsrichtlinien & Genehmigung</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>YouTube-Links</strong> werden automatisch genehmigt und sind sofort sichtbar</li>
            <li>• Andere Links werden vor der Veröffentlichung manuell geprüft</li>
            <li>• Links sollten hochwertige, nützliche Inhalte verlinken</li>
            <li>• Keine Spam, Werbung oder irrelevante Inhalte</li>
            <li>• Beschreibungen sollten ehrlich und hilfreich sein</li>
            <li>• Duplikate werden automatisch erkannt und abgelehnt</li>
          </ul>
        </div>

      </div>
    </div>
  );
} 