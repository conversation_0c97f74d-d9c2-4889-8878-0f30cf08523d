import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../config';
import { Link, LinkWithDetails, CreateLinkForm, PaginatedResponse } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';
import { isTrustedDomain } from '@/utils/domains';
import { userService } from './userService';
import { categoryService } from './categoryService';
import { followService } from './followService';

export const linkService = {
  async create(linkData: CreateLinkForm, userId: string): Promise<string> {
    // Extract domain from URL
    const url = new URL(linkData.url);
    const domain = url.hostname;

    // Check if this is a trusted domain that should be auto-approved
    const isAutoApproved = isTrustedDomain(linkData.url);

    const docRef = await addDoc(collection(db, COLLECTIONS.LINKS), {
      title: linkData.title,
      url: linkData.url,
      description: linkData.description || '',
      domain,
      categoryId: linkData.categoryId,
      submittedBy: userId,
      isApproved: isAutoApproved, // Auto-approve trusted domains (like YouTube)
      isFeatured: false,
      tags: linkData.tags || [],
      submittedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      averageRating: 0,
      totalRatings: 0,
      totalComments: 0,
      // Add approval timestamp if auto-approved
      ...(isAutoApproved && { approvedAt: serverTimestamp() })
    });

    // Increment category link count
    await categoryService.incrementLinkCount(linkData.categoryId);

    return docRef.id;
  },

  async getById(id: string): Promise<Link | null> {
    const docSnap = await getDoc(doc(db, COLLECTIONS.LINKS, id));
    return docSnap.exists() ? serializeDoc<Link>(docSnap) : null;
  },

  async getByUserId(userId: string, includeUnapproved: boolean = false): Promise<LinkWithDetails[]> {
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('submittedBy', '==', userId),
      orderBy('submittedAt', 'desc')
    );

    // Only add approval filter if we don't want to include unapproved links
    if (!includeUnapproved) {
      q = query(q, where('isApproved', '==', true));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));

    return this.enrichLinksWithDetails(links);
  },

  async getByCategory(
    categoryId: string,
    pageSize: number = 20,
    lastDoc?: DocumentSnapshot
  ): Promise<PaginatedResponse<LinkWithDetails>> {
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('categoryId', '==', categoryId),
      where('isApproved', '==', true),
      orderBy('submittedAt', 'desc'),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    const linksWithDetails = await this.enrichLinksWithDetails(links);

    return {
      data: linksWithDetails,
      pagination: {
        page: 1, // This would need to be calculated based on the pagination state
        limit: pageSize,
        total: linksWithDetails.length, // This would need to be calculated separately
        totalPages: Math.ceil(linksWithDetails.length / pageSize),
        hasNext: querySnapshot.docs.length === pageSize,
        hasPrev: false // This would depend on current page
      }
    };
  },

  async getPending(): Promise<LinkWithDetails[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', false),
      orderBy('submittedAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    
    return this.enrichLinksWithDetails(links);
  },

  // Improved Feed Function
  async getFromFollowing(userId: string, options: {
    limitCount?: number;
    includeOwnLinks?: boolean;
    timeRange?: 'day' | 'week' | 'month' | 'all';
    lastDoc?: DocumentSnapshot;
  } = {}): Promise<LinkWithDetails[]> {
    const { limitCount = 20, includeOwnLinks = false, timeRange = 'all' } = options;
    
    console.log('🔄 LinkService.getFromFollowing called for userId:', userId, 'options:', options);

    // Get list of users that this user follows
    let followingIds = await followService.getFollowing(userId);
    
    // Optionally include user's own links
    if (includeOwnLinks) {
      followingIds = [...followingIds, userId];
    }
    
    console.log('📋 Following IDs retrieved:', followingIds);

    if (followingIds.length === 0) {
      console.log('⚠️ User is not following anyone, returning empty array');
      return [];
    }

    // Apply time range filter
    const timeFilter = this.getTimeRangeFilter(timeRange);

    // Get links from followed users (Firestore 'in' query supports max 10 items)
    const chunks = [];
    for (let i = 0; i < followingIds.length; i += 10) {
      chunks.push(followingIds.slice(i, i + 10));
    }

    const allLinks: Link[] = [];

    for (const chunk of chunks) {
      let q = query(
        collection(db, COLLECTIONS.LINKS),
        where('submittedBy', 'in', chunk),
        where('isApproved', '==', true),
        orderBy('submittedAt', 'desc'),
        limit(limitCount * 2) // Get more to account for filtering
      );

      // Add time filter if specified
      if (timeFilter) {
        q = query(q, where('submittedAt', '>=', timeFilter));
      }

      const querySnapshot = await getDocs(q);
      const chunkLinks = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
      allLinks.push(...chunkLinks);
    }

    // Sort all links by submission date, remove duplicates, and limit
    const uniqueLinks = Array.from(
      new Map(allLinks.map(link => [link.id, link])).values()
    );
    
    const sortedLinks = uniqueLinks
      .sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime())
      .slice(0, limitCount);

    console.log(`📊 Found ${sortedLinks.length} links from ${followingIds.length} followed users`);

    return this.enrichLinksWithDetails(sortedLinks);
  },

  // Helper Functions
  async enrichLinksWithDetails(links: Link[]): Promise<LinkWithDetails[]> {
    return Promise.all(
      links.map(async (link) => {
        const [category, submitter] = await Promise.all([
          categoryService.getById(link.categoryId),
          userService.getById(link.submittedBy)
        ]);

        // Use placeholder category if missing
        const effectiveCategory = category || {
          id: 'unknown',
          name: 'Unbekannte Kategorie',
          slug: 'unknown',
          description: 'Kategorie nicht gefunden',
          isActive: false,
          createdBy: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          level: 0,
          path: 'unknown'
        };

        // Use placeholder submitter if missing
        const effectiveSubmitter = submitter || {
          id: 'unknown',
          username: 'unknown-user',
          displayName: 'Unbekannter Benutzer',
          email: '<EMAIL>',
          avatar: undefined,
          bio: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          totalLinks: 0,
          totalFollowers: 0,
          totalFollowing: 0
        };

        return {
          ...link,
          category: effectiveCategory,
          submitter: {
            id: effectiveSubmitter.id,
            username: effectiveSubmitter.username,
            displayName: effectiveSubmitter.displayName,
            avatar: effectiveSubmitter.avatar
          }
        } as LinkWithDetails;
      })
    );
  },

  getTimeRangeFilter(timeRange: 'day' | 'week' | 'month' | 'all'): Date | null {
    if (timeRange === 'all') return null;
    
    const now = new Date();
    switch (timeRange) {
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      default:
        return null;
    }
  },

  // Admin Functions
  async approve(linkId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isApproved: true,
      approvedAt: serverTimestamp()
    });
  },

  async reject(linkId: string): Promise<void> {
    // For now, we'll delete rejected links. In production, you might want to keep them with a rejected status
    await deleteDoc(doc(db, COLLECTIONS.LINKS, linkId));
  },

  async feature(linkId: string, featured: boolean): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      isFeatured: featured
    });
  },

  async getTotalApprovedCount(): Promise<number> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  },

  async getTotalCount(): Promise<number> {
    const querySnapshot = await getDocs(collection(db, COLLECTIONS.LINKS));
    return querySnapshot.size;
  },

  async getAll(options: {
    pageSize?: number;
    sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
  } = {}): Promise<{ data: LinkWithDetails[] }> {
    const { pageSize = 50, sortBy = 'newest' } = options;
    
    let q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      limit(pageSize)
    );

    // Apply sorting
    if (sortBy === 'rating') {
      q = query(q, orderBy('averageRating', 'desc'), orderBy('totalRatings', 'desc'));
    } else if (sortBy === 'popular') {
      q = query(q, orderBy('totalRatings', 'desc'), orderBy('totalComments', 'desc'));
    } else if (sortBy === 'oldest') {
      q = query(q, orderBy('submittedAt', 'asc'));
    } else {
      q = query(q, orderBy('submittedAt', 'desc'));
    }

    const querySnapshot = await getDocs(q);
    const links = querySnapshot.docs.map(doc => serializeDoc<Link>(doc));
    const enrichedLinks = await this.enrichLinksWithDetails(links);

    return { data: enrichedLinks };
  }
}; 