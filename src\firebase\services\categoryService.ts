import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  increment,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Category, CreateCategoryForm, CategoryWithStats } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';
import slugify from 'slugify';

export const categoryService = {
  async create(categoryData: CreateCategoryForm, userId: string): Promise<string> {
    // Check if category name already exists at the same level
    const existingCategory = await this.checkNameExists(categoryData.name, categoryData.parentId);
    if (existingCategory) {
      throw new Error(`Eine Kategorie mit dem Namen "${categoryData.name}" existiert bereits${categoryData.parentId ? ' in dieser Kategorie' : ''}.`);
    }

    const slug = slugify(categoryData.name, { lower: true, strict: true });

    // Build path for hierarchical structure
    let path = slug;
    let level = 0;

    if (categoryData.parentId) {
      const parentCategory = await this.getById(categoryData.parentId);
      if (!parentCategory) {
        throw new Error('Parent category not found');
      }
      path = `${parentCategory.path}/${slug}`;
      level = parentCategory.level + 1;

      // Update parent's subcategory count
      await this.incrementSubcategoryCount(categoryData.parentId);
    }

    const docRef = await addDoc(collection(db, COLLECTIONS.CATEGORIES), {
      ...categoryData,
      slug,
      path,
      level,
      parentId: categoryData.parentId || null,
      isActive: true,
      createdBy: userId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      totalLinks: 0,
      totalSubcategories: 0
    });
    return docRef.id;
  },

  async getById(id: string): Promise<Category | null> {
    const docSnap = await getDoc(doc(db, COLLECTIONS.CATEGORIES, id));
    return docSnap.exists() ? serializeDoc<Category>(docSnap) : null;
  },

  async getAll(): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async getBySlug(slug: string): Promise<Category | null> {
    const q = query(collection(db, COLLECTIONS.CATEGORIES), where('slug', '==', slug));
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : serializeDoc<Category>(querySnapshot.docs[0]);
  },

  async checkSlugExists(slug: string): Promise<boolean> {
    const category = await this.getBySlug(slug);
    return category !== null;
  },

  async incrementLinkCount(categoryId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, categoryId), {
      totalLinks: increment(1)
    });
  },

  async update(id: string, data: Partial<Category>): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, id), {
      ...data,
      updatedAt: serverTimestamp()
    });
  },

  async delete(id: string): Promise<void> {
    await deleteDoc(doc(db, COLLECTIONS.CATEGORIES, id));
  },

  async getRecent(limitCount: number = 5): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async getByUser(userId: string): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('createdBy', '==', userId),
      where('isActive', '==', true),
      orderBy('level'),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async checkNameExists(name: string, parentId?: string): Promise<boolean> {
    let q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('name', '==', name),
      where('isActive', '==', true)
    );

    if (parentId) {
      q = query(q, where('parentId', '==', parentId));
    } else {
      q = query(q, where('parentId', '==', null));
    }

    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  },

  async getSubcategories(parentId: string): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('parentId', '==', parentId),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async getRootCategories(): Promise<Category[]> {
    const q = query(
      collection(db, COLLECTIONS.CATEGORIES),
      where('parentId', '==', null),
      where('isActive', '==', true),
      orderBy('name')
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Category>(doc));
  },

  async incrementSubcategoryCount(categoryId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, categoryId), {
      totalSubcategories: increment(1)
    });
  },

  async decrementSubcategoryCount(categoryId: string): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.CATEGORIES, categoryId), {
      totalSubcategories: increment(-1)
    });
  },

  async getCategoryWithHierarchy(categoryId: string): Promise<CategoryWithStats | null> {
    const category = await this.getById(categoryId);
    if (!category) return null;

    const [subcategories, parentCategory] = await Promise.all([
      this.getSubcategories(categoryId),
      category.parentId ? this.getById(category.parentId) : Promise.resolve(null)
    ]);

    return {
      ...category,
      subcategories,
      parentCategory: parentCategory || undefined
    } as CategoryWithStats;
  }
}; 