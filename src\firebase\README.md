# Firebase Firestore Services

Die ursprüngliche `firestore.ts` <PERSON><PERSON> (1548 Zeilen) wurde in modulare Services aufgeteilt für bessere Wartbarkeit und Performance.

## 📁 Struktur

```
src/firebase/
├── config.ts                    # Firebase Konfiguration
├── firestore.ts                 # Legacy (Backup der ursprünglichen Datei)
├── firestore-new.ts            # Neue modulare Exports
├── services/                    # Modulare Services
│   ├── userService.ts           # Benutzer-Operationen
│   ├── categoryService.ts       # Kategorie-Management
│   ├── linkService.ts           # Link-Management mit Feed
│   ├── ratingService.ts         # Bewertungssystem
│   ├── commentService.ts        # Kommentar-System
│   ├── followService.ts         # Follow/Unfollow System
│   ├── favoriteService.ts       # Favoriten-Management
│   ├── reportService.ts         # Meldungs-System
│   └── feedService.ts           # Personalisierter Feed
├── utils/
│   └── firestore-helpers.ts     # Gemeinsame Hilfsfunktionen
└── types/
    └── collections.ts           # Collection-Konstanten
```

## 🚀 Migration

### Alte Imports (vor Migration):
```typescript
import { userService, linkService } from '@/firebase/firestore';
```

### Neue Imports (nach Migration):
```typescript
import { userService, linkService } from '@/firebase/firestore-new';
```

## 📋 Services

### UserService
- `create()` - Benutzer erstellen
- `getById()` - Benutzer laden
- `update()` - Benutzer aktualisieren
- `checkUsernameExists()` - Username-Verfügbarkeit prüfen

### CategoryService
- `create()` - Kategorie erstellen
- `getAll()` - Alle Kategorien
- `getBySlug()` - Nach Slug suchen
- `getRootCategories()` - Hauptkategorien
- `getSubcategories()` - Unterkategorien

### LinkService
- `create()` - Link einreichen
- `getById()` - Link laden
- `getByUserId()` - Benutzer-Links
- `getByCategory()` - Links nach Kategorie
- `getPending()` - Wartende Links (Admin)
- `getFromFollowing()` - 🆕 Verbesserte Feed-Funktion
- `approve()` / `reject()` - Admin-Aktionen

### FollowService ⭐
- `follow()` - Benutzer folgen
- `unfollow()` - Entfolgen
- `isFollowing()` - Follow-Status prüfen
- `getFollowing()` - Gefolgte Benutzer
- `getFollowers()` - Follower-Liste
- `getFollowStats()` - 🆕 Follow-Statistiken

### FeedService 🆕
**Neuer spezialisierter Service für optimierte Feed-Verwaltung:**

- `getPersonalizedFeed()` - Personalisierter Feed basierend auf Follows
- `getDiscoveryFeed()` - Discovery Feed für neue Benutzer
- `getTrendingLinks()` - Trending Content
- `getFeedStats()` - Feed-Analytik

### RatingService
- `create()` - Bewertung abgeben
- `updateAverageRating()` - Durchschnittsbewertung aktualisieren
- `getUserRating()` - Benutzerbewertung laden
- `getTopRatedLinks()` - 🆕 Top bewertete Links

### CommentService
- `create()` - Kommentar erstellen
- `getByLink()` - Kommentare zu Link
- `getByLinkNested()` - 🆕 Verschachtelte Kommentare
- `approve()` - Kommentar genehmigen

### FavoriteService
- `add()` / `remove()` - Favoriten hinzufügen/entfernen
- `isFavorited()` - Favoriten-Status prüfen
- `getUserFavorites()` - Benutzer-Favoriten

### ReportService
- `create()` - Meldung erstellen
- `getPending()` - Wartende Meldungen
- `updateStatus()` - Status aktualisieren
- `getReportStats()` - 🆕 Meldungs-Statistiken

## 🎯 Verbesserungen

### 1. Feed-System
- **Optimierte Follow-Funktion**: Chunked Queries für > 10 Follows
- **Personalisierter Feed**: Kombiniert Follows + Empfehlungen
- **Discovery Mode**: Für neue Benutzer ohne Follows
- **Zeit-Filter**: Tag/Woche/Monat Optionen
- **Sortierung**: Neueste, Bewertung, Beliebtheit

### 2. Performance
- **Tree Shaking**: Nur verwendete Services werden geladen
- **Parallel Queries**: Gleichzeitige Datenabfragen
- **Caching-Ready**: Einfacher Cache-Layer möglich
- **Kleinere Bundles**: Modulare Imports

### 3. Developer Experience
- **Bessere Typisierung**: TypeScript-optimiert
- **Einheitliche APIs**: Konsistente Service-Pattern
- **Einfache Tests**: Isolierte Service-Tests
- **Klare Struktur**: Logische Aufteilung

## 🔧 Verwendung

### Feed implementieren:
```typescript
import { feedService } from '@/firebase/firestore-new';

// Personalisierter Feed
const feed = await feedService.getPersonalizedFeed({
  userId: 'user123',
  pageSize: 20,
  timeRange: 'week',
  includeOwnLinks: false
});

// Discovery Feed für neue Benutzer
const discovery = await feedService.getDiscoveryFeed({
  userId: 'user123',
  sortBy: 'popular'
});
```

### Follow-System:
```typescript
import { followService } from '@/firebase/firestore-new';

// Benutzer folgen
await followService.follow('follower123', 'following456');

// Follow-Status prüfen
const isFollowing = await followService.isFollowing('user1', 'user2');

// Follow-Statistiken
const stats = await followService.getFollowStats('user123');
```

## 📊 Vorteile

1. **Wartbarkeit**: 9 kleine Dateien statt 1 große (1548 Zeilen)
2. **Performance**: Tree Shaking, parallele Imports
3. **Testbarkeit**: Isolierte Service-Tests möglich
4. **Skalierbarkeit**: Einfache Erweiterung neuer Features
5. **Developer Experience**: Bessere IDE-Unterstützung, IntelliSense 

// Beispiel für neue Service-Importe:
import { userService } from '@/services/userService';
import { linkService } from '@/services/linkService';
import { followService } from '@/services/followService';
import { categoryService } from '@/services/categoryService';
import { ratingService } from '@/services/ratingService';
import { favoriteService } from '@/services/favoriteService';
import { reportService } from '@/firebase/services/reportService';
import { feedService } from '@/firebase/services/feedService'; 