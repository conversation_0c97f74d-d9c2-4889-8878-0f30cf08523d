'use client';

import React from 'react';
import Link from 'next/link';
import { LinkWithDetails } from '@/types';
import { 
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { 
  StarIcon as StarIconSolid
} from '@heroicons/react/24/solid';
import { formatRelativeTime, extractDomain, truncateText, getInitials } from '@/utils/helpers';

interface AdminLinkCardProps {
  link: LinkWithDetails;
  isSelected: boolean;
  onSelect: (linkId: string, selected: boolean) => void;
  onApprove?: (linkId: string) => void;
  onReject?: (linkId: string) => void;
  onFeature?: (linkId: string, featured: boolean) => void;
  showActions?: boolean;
}

const AdminLinkCard: React.FC<AdminLinkCardProps> = ({ 
  link, 
  isSelected,
  onSelect,
  onApprove,
  onReject,
  onFeature,
  showActions = true
}) => {

  return (
    <div className={`bg-white rounded-lg border transition-all duration-200 ${
      isSelected 
        ? 'border-blue-500 ring-2 ring-blue-200' 
        : 'border-gray-200 hover:border-gray-300'
    }`}>
      <div className="p-6">
        {/* Header with checkbox and status */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-3">
            {/* Checkbox */}
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(link.id, e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            
            <div className="flex-1">
              {/* Status badges */}
              <div className="flex items-center space-x-2 mb-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  link.isApproved 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {link.isApproved ? (
                    <>
                      <CheckCircleIcon className="h-3 w-3 mr-1" />
                      Genehmigt
                    </>
                  ) : (
                    <>
                      <ClockIcon className="h-3 w-3 mr-1" />
                      Wartend
                    </>
                  )}
                </span>
                
                {link.isFeatured && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <StarIconSolid className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                )}
                
                <Link
                  href={`/category/${link.category.slug}`}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                >
                  {link.category.name}
                </Link>
              </div>
              
              {/* Submitter and date */}
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Link
                  href={`/profile?user=${link.submitter.username}`}
                  className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
                >
                  {link.submitter.avatar ? (
                    <img
                      src={link.submitter.avatar}
                      alt={link.submitter.displayName}
                      className="h-4 w-4 rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-4 w-4 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs">
                      {getInitials(link.submitter.displayName)}
                    </div>
                  )}
                  <span>{link.submitter.displayName}</span>
                </Link>
                <span>•</span>
                <span>{formatRelativeTime(link.submittedAt)}</span>
                {link.isApproved && link.approvedAt && (
                  <>
                    <span>•</span>
                    <span>Genehmigt {formatRelativeTime(link.approvedAt)}</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Title and Description */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-6">
            {link.title}
          </h3>
          {link.description && (
            <p className="text-gray-600 text-sm leading-5">
              {truncateText(link.description, 200)}
            </p>
          )}
        </div>

        {/* URL */}
        <div className="mb-4">
          <a
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-colors group"
          >
            <span className="text-sm font-medium">{extractDomain(link.url)}</span>
            <ArrowTopRightOnSquareIcon className="h-4 w-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
          </a>
        </div>

        {/* Stats */}
        <div className="mb-4 flex items-center space-x-4 text-sm text-gray-500">
          <span>⭐ {link.averageRating?.toFixed(1) || '0.0'} ({link.totalRatings || 0})</span>
          <span>💬 {link.totalComments || 0} Kommentare</span>
          {link.tags && link.tags.length > 0 && (
            <span>🏷️ {link.tags.length} Tags</span>
          )}
        </div>

        {/* Tags */}
        {link.tags && link.tags.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {link.tags.slice(0, 5).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {tag}
                </span>
              ))}
              {link.tags.length > 5 && (
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-500">
                  +{link.tags.length - 5} mehr
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2">
              {!link.isApproved && onApprove && (
                <button
                  onClick={() => onApprove(link.id)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"
                >
                  <CheckCircleIcon className="h-4 w-4 mr-2" />
                  Genehmigen
                </button>
              )}
              
              {!link.isApproved && onReject && (
                <button
                  onClick={() => onReject(link.id)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors"
                >
                  <XCircleIcon className="h-4 w-4 mr-2" />
                  Ablehnen
                </button>
              )}
              
              {link.isApproved && onFeature && (
                <button
                  onClick={() => onFeature(link.id, !link.isFeatured)}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md transition-colors ${
                    link.isFeatured
                      ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                      : 'text-white bg-yellow-600 hover:bg-yellow-700'
                  }`}
                >
                  <StarIcon className="h-4 w-4 mr-2" />
                  {link.isFeatured ? 'Unfeatured' : 'Featured'}
                </button>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Link
                href={`/link/${link.id}`}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                <EyeIcon className="h-4 w-4 mr-2" />
                Ansehen
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminLinkCard;
