import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  setDoc
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import { User } from '@/types';
import { COLLECTIONS } from '@/firebase/types/collections';
import { serializeDoc } from '@/firebase/utils/firestore-helpers';

export const userService = {
  async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>, customId?: string): Promise<string> {
    console.log('👤 UserService.create called with data:', userData);
    console.log('👤 Custom ID provided:', customId);
    
    if (customId) {
      // Use specific document ID (for Firebase Auth UID)
      await setDoc(doc(db, COLLECTIONS.USERS, customId), {
        ...userData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalLinks: 0,
        totalFollowers: 0,
        totalFollowing: 0
      });
      console.log('👤 User created with custom ID:', customId);
      return customId;
    } else {
      // Auto-generate ID
      const docRef = await addDoc(collection(db, COLLECTIONS.USERS), {
        ...userData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        totalLinks: 0,
        totalFollowers: 0,
        totalFollowing: 0
      });
      console.log('👤 User created with auto-generated ID:', docRef.id);
      return docRef.id;
    }
  },

  async getById(id: string): Promise<User | null> {
    console.log('👤 UserService.getById called with id:', id);
    try {
      const docSnap = await getDoc(doc(db, COLLECTIONS.USERS, id));
      console.log('👤 Document exists:', docSnap.exists());
      if (docSnap.exists()) {
        const userData = serializeDoc<User>(docSnap);
        console.log('👤 User data loaded:', userData);
        return userData;
      } else {
        console.log('👤 No user found with id:', id);
        return null;
      }
    } catch (error) {
      console.error('🚨 Error in userService.getById:', error);
      return null;
    }
  },

  async getByUsername(username: string): Promise<User | null> {
    console.log('👤 UserService.getByUsername called with username:', username);
    try {
      const q = query(collection(db, COLLECTIONS.USERS), where('username', '==', username));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        console.log('👤 No user found with username:', username);
        return null;
      }
      
      const userData = serializeDoc<User>(querySnapshot.docs[0]);
      console.log('👤 User found by username:', userData);
      return userData;
    } catch (error) {
      console.error('🚨 Error in userService.getByUsername:', error);
      return null;
    }
  },

  async update(id: string, data: Partial<User>): Promise<void> {
    console.log('👤 UserService.update called:', { id, data });
    try {
      await updateDoc(doc(db, COLLECTIONS.USERS, id), {
        ...data,
        updatedAt: serverTimestamp()
      });
      console.log('✅ User updated successfully');
    } catch (error) {
      console.error('🚨 Error updating user:', error);
      throw error;
    }
  },

  async checkUsernameExists(username: string): Promise<boolean> {
    const user = await this.getByUsername(username);
    return user !== null;
  },

  async getTotalCount(): Promise<number> {
    try {
      const querySnapshot = await getDocs(collection(db, COLLECTIONS.USERS));
      return querySnapshot.size;
    } catch (error) {
      console.error('🚨 Error getting user count:', error);
      return 0;
    }
  }
};
