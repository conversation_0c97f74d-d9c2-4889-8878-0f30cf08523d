import { notFound } from 'next/navigation';
import { linkService } from '@/firebase/services/linkService';
import LinkCard from '@/components/UI/LinkCard';
import { LinkWithDetails } from '@/types';

interface LinkDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function LinkDetailPage({ params }: LinkDetailPageProps) {
  const { id } = await params;
  const link = await linkService.getById(id);
  if (!link) return notFound();

  // enrichLinksWithDetails erwartet ein Array
  const [linkWithDetails]: LinkWithDetails[] = await linkService.enrichLinksWithDetails([link]);
  if (!linkWithDetails) return notFound();

  return (
    <div className="min-h-screen flex items-center justify-center bg-black">
      <div className="max-w-xl w-full">
        <LinkCard link={linkWithDetails} />
      </div>
    </div>
  );
} 