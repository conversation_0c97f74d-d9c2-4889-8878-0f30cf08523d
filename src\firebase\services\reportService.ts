import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Report } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';

export const reportService = {
  async create(
    targetType: 'link' | 'comment',
    targetId: string,
    reportedBy: string,
    reason: string,
    customReason?: string,
    description?: string
  ): Promise<string> {
    const docRef = await addDoc(collection(db, COLLECTIONS.REPORTS), {
      reportedBy,
      targetType,
      targetId,
      reason,
      customReason: customReason || null,
      description: description || null,
      status: 'pending',
      createdAt: serverTimestamp()
    });
    
    return docRef.id;
  },

  async getByUser(userId: string): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('reportedBy', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getPending(): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('status', '==', 'pending'),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getAll(): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async updateStatus(
    reportId: string,
    status: 'reviewed' | 'resolved' | 'dismissed',
    reviewedBy: string,
    reviewNotes?: string
  ): Promise<void> {
    await updateDoc(doc(db, COLLECTIONS.REPORTS, reportId), {
      status,
      reviewedBy,
      reviewedAt: serverTimestamp(),
      reviewNotes: reviewNotes || null
    });
  },

  async hasUserReported(userId: string, targetType: string, targetId: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('reportedBy', '==', userId),
      where('targetType', '==', targetType),
      where('targetId', '==', targetId)
    );
    
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  },

  async getReportsForTarget(targetType: 'link' | 'comment', targetId: string): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('targetType', '==', targetType),
      where('targetId', '==', targetId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getRecent(limitCount: number = 5): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getByStatus(status: 'pending' | 'reviewed' | 'resolved' | 'dismissed'): Promise<Report[]> {
    const q = query(
      collection(db, COLLECTIONS.REPORTS),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Report>(doc));
  },

  async getReportStats(): Promise<{
    pending: number;
    reviewed: number;
    resolved: number;
    dismissed: number;
    total: number;
  }> {
    const allReports = await this.getAll();
    
    const stats = {
      pending: 0,
      reviewed: 0,
      resolved: 0,
      dismissed: 0,
      total: allReports.length
    };

    allReports.forEach(report => {
      stats[report.status as keyof typeof stats]++;
    });

    return stats;
  }
}; 