import { useState, useEffect, useCallback } from 'react';
import { linkService } from '@/services/linkService';
import { LinkWithDetails } from '@/types';
import { DocumentSnapshot } from 'firebase/firestore';

interface UseSearchOptions {
  sortBy?: 'newest' | 'oldest' | 'rating' | 'popular';
  categoryId?: string;
  pageSize?: number;
  debounceMs?: number;
}

interface SearchState {
  results: LinkWithDetails[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  totalCount: number;
  query: string;
}

export const useSearch = ({
  sortBy = 'newest',
  categoryId,
  pageSize = 20,
  debounceMs = 300
}: UseSearchOptions = {}) => {
  const [state, setState] = useState<SearchState>({
    results: [],
    loading: false,
    error: null,
    hasMore: false,
    totalCount: 0,
    query: ''
  });
  
  const [lastDoc, setLastDoc] = useState<DocumentSnapshot | null>(null);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // Debounced search function
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const performSearch = useCallback(async (
    query: string, 
    reset: boolean = true,
    options: { sortBy?: typeof sortBy; categoryId?: string } = {}
  ) => {
    if (!query.trim()) {
      setState(prev => ({
        ...prev,
        results: [],
        loading: false,
        error: null,
        hasMore: false,
        totalCount: 0,
        query: ''
      }));
      return;
    }

    try {
      setState(prev => ({ 
        ...prev, 
        loading: true, 
        error: null,
        query: reset ? query : prev.query
      }));

      const response = await linkService.search(query, {
        sortBy: options.sortBy || sortBy,
        categoryId: options.categoryId || categoryId,
        pageSize,
        lastDoc: reset ? undefined : lastDoc || undefined
      });

      setState(prev => ({
        ...prev,
        results: reset ? response.data : [...prev.results, ...response.data],
        hasMore: response.pagination.hasNext,
        totalCount: response.pagination.total,
        loading: false
      }));

      if (reset) {
        setLastDoc(null);
        // Add to search history
        setSearchHistory(prev => {
          const newHistory = [query, ...prev.filter(h => h !== query)].slice(0, 10);
          // Save to localStorage
          try {
            localStorage.setItem('deeplinked_search_history', JSON.stringify(newHistory));
          } catch {
            // Ignore localStorage errors
          }
          return newHistory;
        });
      }

    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Suchfehler aufgetreten'
      }));
    }
  }, [sortBy, categoryId, pageSize, lastDoc]);

  const search = useCallback((query: string, immediate: boolean = false) => {
    // Clear existing timer
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    if (immediate) {
      performSearch(query, true);
    } else {
      // Set new timer
      const timer = setTimeout(() => {
        performSearch(query, true);
      }, debounceMs);
      setDebounceTimer(timer);
    }
  }, [debounceTimer, debounceMs, performSearch]);

  const loadMore = useCallback(() => {
    if (state.hasMore && !state.loading && state.query) {
      performSearch(state.query, false);
    }
  }, [state.hasMore, state.loading, state.query, performSearch]);

  const clearSearch = useCallback(() => {
    setState({
      results: [],
      loading: false,
      error: null,
      hasMore: false,
      totalCount: 0,
      query: ''
    });
    setLastDoc(null);
  }, []);

  const searchWithFilters = useCallback((
    query: string, 
    filters: { sortBy?: typeof sortBy; categoryId?: string }
  ) => {
    performSearch(query, true, filters);
  }, [performSearch]);

  // Load search history from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem('deeplinked_search_history');
      if (saved) {
        setSearchHistory(JSON.parse(saved));
      }
    } catch {
      // Ignore localStorage errors
    }
  }, []);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return {
    ...state,
    search,
    loadMore,
    clearSearch,
    searchWithFilters,
    searchHistory,
    isSearching: state.loading && state.query.length > 0
  };
};
