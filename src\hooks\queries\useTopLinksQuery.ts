'use client';

import { useQuery } from '@tanstack/react-query';
import { linkService } from '@/services/linkService';
import { LinkWithDetails } from '@/types';

export const useTopLinksQuery = (limit: number = 6, timeframe: 'week' | 'month' | 'all' = 'week') => {
  return useQuery({
    queryKey: ['topLinks', limit, timeframe],
    queryFn: async (): Promise<LinkWithDetails[]> => {
      // Calculate date for timeframe
      const now = new Date();
      let startDate: Date | undefined;
      
      if (timeframe === 'week') {
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      } else if (timeframe === 'month') {
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }
      
      // Get top-rated links
      const links = await linkService.getTopRatedLinks(limit, startDate);
      return links;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
