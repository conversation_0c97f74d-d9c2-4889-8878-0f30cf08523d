'use client';

import React from 'react';
import {
  ChartBarIcon,
  UserGroupIcon,
  LinkIcon,
  FolderIcon,
  CheckCircleIcon,
  FlagIcon
} from '@heroicons/react/24/outline';
import { formatRelativeTime } from '@/utils/helpers';

interface AdminStats {
  totalLinks: number;
  pendingLinks: number;
  totalCategories: number;
  totalUsers: number;
  pendingReports: number;
  thisWeekSubmissions: number;
}

interface RecentActivity {
  id: string;
  type: 'link_approved' | 'link_submitted' | 'category_created' | 'report_submitted';
  title: string;
  timestamp: Date;
  color: string;
}

interface AdminOverviewProps {
  stats: AdminStats;
  recentActivities: RecentActivity[];
  onTabChange: (tab: string) => void;
}

const AdminOverview: React.FC<AdminOverviewProps> = ({
  stats,
  recentActivities,
  onTabChange
}) => {
  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <LinkIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Gesamt Links</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalLinks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChartBarIcon className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Warteschlange</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.pendingLinks}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FolderIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Kategorien</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalCategories}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserGroupIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Benutzer</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalUsers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <FlagIcon className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Meldungen</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.pendingReports}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Schnellaktionen</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => onTabChange('links')}
            className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <CheckCircleIcon className="h-6 w-6 text-green-600 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Links genehmigen</p>
              <p className="text-sm text-gray-600">{stats.pendingLinks} wartend</p>
            </div>
          </button>
          
          <button
            onClick={() => onTabChange('categories')}
            className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FolderIcon className="h-6 w-6 text-blue-600 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Kategorien verwalten</p>
              <p className="text-sm text-gray-600">{stats.totalCategories} aktiv</p>
            </div>
          </button>
          
          <button
            onClick={() => onTabChange('users')}
            className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <UserGroupIcon className="h-6 w-6 text-purple-600 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Benutzer verwalten</p>
              <p className="text-sm text-gray-600">Rollen & Rechte</p>
            </div>
          </button>
          
          <button
            onClick={() => onTabChange('reports')}
            className="flex items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FlagIcon className="h-6 w-6 text-red-600 mr-3" />
            <div className="text-left">
              <p className="font-medium text-gray-900">Meldungen bearbeiten</p>
              <p className="text-sm text-gray-600">{stats.pendingReports} ausstehend</p>
            </div>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Letzte Aktivitäten</h3>
        <div className="space-y-3">
          {recentActivities.length === 0 ? (
            <p className="text-gray-500 text-center py-4">Keine aktuellen Aktivitäten</p>
          ) : (
            recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center text-sm">
                <div className={`h-2 w-2 rounded-full mr-3 ${
                  activity.color === 'green' ? 'bg-green-400' :
                  activity.color === 'blue' ? 'bg-blue-400' :
                  activity.color === 'yellow' ? 'bg-yellow-400' :
                  activity.color === 'red' ? 'bg-red-400' :
                  'bg-gray-400'
                }`}></div>
                <span className="text-gray-600">{activity.title}</span>
                <span className="ml-auto text-gray-400">
                  {formatRelativeTime(activity.timestamp)}
                </span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminOverview;
