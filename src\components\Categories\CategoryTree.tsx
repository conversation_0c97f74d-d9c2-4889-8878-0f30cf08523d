'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Category } from '@/types';
import {
  ChevronRightIcon,
  ChevronDownIcon,
  FolderIcon,
  FolderOpenIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface CategoryTreeProps {
  categories: Category[];
  onCreateSubcategory?: (parentCategory: Category) => void;
  showCreateButton?: boolean;
  currentUserId?: string;
}

interface CategoryNodeProps {
  category: Category;
  subcategories: Category[];
  onCreateSubcategory?: (parentCategory: Category) => void;
  showCreateButton?: boolean;
  currentUserId?: string;
  level?: number;
}

const CategoryNode: React.FC<CategoryNodeProps> = ({
  category,
  subcategories,
  onCreateSubcategory,
  showCreateButton,
  currentUserId,
  level = 0
}) => {
  const [isExpanded, setIsExpanded] = useState(level === 0);
  const hasSubcategories = subcategories.length > 0;
  const canCreateSubcategory = showCreateButton && currentUserId;

  return (
    <div className="w-full">
      <div 
        className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
          level === 0 
            ? 'bg-white border-gray-200 hover:border-blue-300 hover:shadow-md' 
            : 'bg-gray-50 border-gray-100 hover:bg-gray-100'
        }`}
        style={{ marginLeft: `${level * 20}px` }}
      >
        <div className="flex items-center space-x-3 flex-1">
          {/* Expand/Collapse Button */}
          {hasSubcategories ? (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
            >
              {isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-600" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-600" />
              )}
            </button>
          ) : (
            <div className="w-6" /> // Spacer for alignment
          )}

          {/* Category Icon */}
          {isExpanded && hasSubcategories ? (
            <FolderOpenIcon className="h-5 w-5 text-blue-600 flex-shrink-0" />
          ) : (
            <FolderIcon className="h-5 w-5 text-blue-600 flex-shrink-0" />
          )}

          {/* Category Info */}
          <Link
            href={`/category/${category.slug}`}
            className="flex-1 min-w-0"
          >
            <div className="flex items-center justify-between">
              <div className="min-w-0 flex-1">
                <h3 className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors truncate">
                  {category.name}
                </h3>
                {category.description && (
                  <p className="text-xs text-gray-500 truncate mt-1">
                    {category.description}
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-4 ml-4">
                <span className="text-xs text-gray-500">
                  {category.totalLinks || 0} Links
                </span>
                {hasSubcategories && (
                  <span className="text-xs text-gray-500">
                    {subcategories.length} Unterkategorien
                  </span>
                )}
                {category.color && (
                  <div 
                    className="w-3 h-3 rounded-full border border-gray-200 flex-shrink-0"
                    style={{ backgroundColor: category.color }}
                  />
                )}
              </div>
            </div>
          </Link>
        </div>

        {/* Create Subcategory Button */}
        {canCreateSubcategory && (
          <button
            onClick={() => onCreateSubcategory?.(category)}
            className="ml-2 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
            title="Unterkategorie erstellen"
          >
            <PlusIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Subcategories */}
      {isExpanded && hasSubcategories && (
        <div className="mt-2 space-y-1">
          {subcategories.map((subcategory) => (
            <CategoryNode
              key={subcategory.id}
              category={subcategory}
              subcategories={[]} // For now, we only show 2 levels
              onCreateSubcategory={onCreateSubcategory}
              showCreateButton={showCreateButton}
              currentUserId={currentUserId}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const CategoryTree: React.FC<CategoryTreeProps> = ({
  categories,
  onCreateSubcategory,
  showCreateButton = false,
  currentUserId
}) => {
  // Group categories by parent
  const rootCategories = categories.filter(cat => !cat.parentId);
  const subcategoriesMap = categories.reduce((acc, cat) => {
    if (cat.parentId) {
      if (!acc[cat.parentId]) {
        acc[cat.parentId] = [];
      }
      acc[cat.parentId].push(cat);
    }
    return acc;
  }, {} as Record<string, Category[]>);

  if (rootCategories.length === 0) {
    return (
      <div className="text-center py-8">
        <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-500">Noch keine Kategorien vorhanden</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {rootCategories.map((category) => (
        <CategoryNode
          key={category.id}
          category={category}
          subcategories={subcategoriesMap[category.id] || []}
          onCreateSubcategory={onCreateSubcategory}
          showCreateButton={showCreateButton}
          currentUserId={currentUserId}
          level={0}
        />
      ))}
    </div>
  );
};

export default CategoryTree;
