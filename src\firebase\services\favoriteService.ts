import {
  collection,
  addDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  orderBy,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../config';
import { Favorite } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';

export const favoriteService = {
  async add(userId: string, linkId: string): Promise<void> {
    await addDoc(collection(db, COLLECTIONS.FAVORITES), {
      userId,
      linkId,
      createdAt: serverTimestamp()
    });
  },

  async remove(userId: string, linkId: string): Promise<void> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      where('linkId', '==', linkId)
    );
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      await deleteDoc(querySnapshot.docs[0].ref);
    }
  },

  async isFavorited(userId: string, linkId: string): Promise<boolean> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      where('linkId', '==', linkId)
    );
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  },

  async getUserFavorites(userId: string): Promise<Favorite[]> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => serializeDoc<Favorite>(doc));
  },

  async getFavoriteCount(linkId: string): Promise<number> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('linkId', '==', linkId)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.size;
  },

  async getUserFavoriteLinks(userId: string): Promise<string[]> {
    const q = query(
      collection(db, COLLECTIONS.FAVORITES),
      where('userId', '==', userId)
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => doc.data().linkId);
  }
}; 