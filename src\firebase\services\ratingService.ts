import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { db } from '../config';
import { Rating } from '@/types';
import { COLLECTIONS } from '../types/collections';
import { serializeDoc } from '../utils/firestore-helpers';

export const ratingService = {
  async create(linkId: string, userId: string, rating: number): Promise<void> {
    // Validate rating range
    if (rating < 1 || rating > 5) {
      throw new Error('Rating muss zwischen 1 und 5 liegen');
    }

    // Check if user has already rated this link
    const existingRating = await this.getUserRating(linkId, userId);
    
    if (existingRating) {
      // Update existing rating
      await updateDoc(doc(db, COLLECTIONS.RATINGS, existingRating.id), {
        rating,
        updatedAt: serverTimestamp()
      });
    } else {
      // Create new rating
      await addDoc(collection(db, COLLECTIONS.RATINGS), {
        linkId,
        userId,
        rating,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Increment total ratings count
      await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
        totalRatings: increment(1)
      });
    }

    // Update average rating
    await this.updateAverageRating(linkId);
  },

  async updateAverageRating(linkId: string): Promise<void> {
    const q = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      // No ratings, set average to 0
      await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
        averageRating: 0,
        totalRatings: 0
      });
      return;
    }

    const ratings = querySnapshot.docs.map(doc => doc.data().rating);
    const averageRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;

    await updateDoc(doc(db, COLLECTIONS.LINKS, linkId), {
      averageRating,
      totalRatings: ratings.length
    });
  },

  async getUserRating(linkId: string, userId: string): Promise<Rating | null> {
    const q = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId),
      where('userId', '==', userId)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.empty ? null : serializeDoc<Rating>(querySnapshot.docs[0]);
  },

  async getLinkRatings(linkId: string): Promise<Rating[]> {
    const q = query(
      collection(db, COLLECTIONS.RATINGS),
      where('linkId', '==', linkId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Rating>(doc));
  },

  async getUserRatings(userId: string): Promise<Rating[]> {
    const q = query(
      collection(db, COLLECTIONS.RATINGS),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => serializeDoc<Rating>(doc));
  },

  async getTopRatedLinks(limitCount: number = 10): Promise<{ linkId: string; averageRating: number; totalRatings: number }[]> {
    const q = query(
      collection(db, COLLECTIONS.LINKS),
      where('isApproved', '==', true),
      where('totalRatings', '>', 0),
      orderBy('averageRating', 'desc'),
      orderBy('totalRatings', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      linkId: doc.id,
      averageRating: doc.data().averageRating || 0,
      totalRatings: doc.data().totalRatings || 0
    }));
  },

  async getRatingStats(linkId: string): Promise<{
    average: number;
    total: number;
    distribution: { [key: number]: number };
  }> {
    const ratings = await this.getLinkRatings(linkId);
    
    if (ratings.length === 0) {
      return {
        average: 0,
        total: 0,
        distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };
    }

    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    let sum = 0;

    ratings.forEach(rating => {
      distribution[rating.rating as keyof typeof distribution]++;
      sum += rating.rating;
    });

    return {
      average: sum / ratings.length,
      total: ratings.length,
      distribution
    };
  }
}; 