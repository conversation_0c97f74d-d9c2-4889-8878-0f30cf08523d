import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { categoryService } from '@/services/categoryService';

// Query Keys
export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...categoryKeys.lists(), filters] as const,
  details: () => [...categoryKeys.all, 'detail'] as const,
  detail: (id: string) => [...categoryKeys.details(), id] as const,
  bySlug: (slug: string) => [...categoryKeys.all, 'slug', slug] as const,
};

// All Categories Query
export const useCategoriesQuery = () => {
  return useQuery({
    queryKey: categoryKeys.list(),
    queryFn: () => categoryService.getAll(),
    staleTime: 10 * 60 * 1000, // 10 minutes (categories don't change often)
  });
};

// Single Category Query
export const useCategoryQuery = (categoryId: string) => {
  return useQuery({
    queryKey: categoryKeys.detail(categoryId),
    queryFn: () => categoryService.getById(categoryId),
    enabled: !!categoryId,
    staleTime: 10 * 60 * 1000,
  });
};

// Category by Slug Query
export const useCategoryBySlugQuery = (slug: string) => {
  return useQuery({
    queryKey: categoryKeys.bySlug(slug),
    queryFn: () => categoryService.getBySlug(slug),
    enabled: !!slug,
    staleTime: 10 * 60 * 1000,
  });
};

// Category Mutations
export const useCategoryMutations = () => {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: ({ categoryData, userId }: { categoryData: Record<string, unknown>; userId: string }) =>
      categoryService.create(categoryData, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ categoryId, updates }: { categoryId: string; updates: Record<string, unknown> }) =>
      categoryService.update(categoryId, updates),
    onSuccess: (_, { categoryId }) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.detail(categoryId) });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (categoryId: string) => categoryService.delete(categoryId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
    },
  });

  return {
    create: createMutation,
    update: updateMutation,
    delete: deleteMutation,
  };
};
