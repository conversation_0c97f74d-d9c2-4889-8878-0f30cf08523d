'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { categoryService } from '@/services/categoryService';
import { Category } from '@/types';
import {
  FolderIcon,
  FolderPlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import CreateCategoryModal from '@/components/Categories/CreateCategoryModal';
import Link from 'next/link';

export default function MyCategoriesPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const fetchMyCategories = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      // Get categories created by the current user
      const myCategories = await categoryService.getByUserId(user.id);
      setCategories(myCategories);
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/login');
    }
  }, [user, router]);

  // Fetch user's categories
  useEffect(() => {
    if (user) {
      fetchMyCategories();
    }
  }, [user, fetchMyCategories]);

  const handleCategoryCreated = () => {
    fetchMyCategories();
  };

  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!confirm(`Bist du sicher, dass du die Kategorie "${categoryName}" löschen möchtest?`)) {
      return;
    }

    try {
      await categoryService.delete(categoryId);
      fetchMyCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
      alert('Fehler beim Löschen der Kategorie');
    }
  };

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Meine Kategorien</h1>
              <p className="mt-2 text-gray-600">
                Verwalte deine erstellten Kategorien
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <FolderPlusIcon className="h-4 w-4 mr-2" />
              Neue Kategorie
            </button>
          </div>
        </div>

        {/* Categories List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : categories.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category) => (
              <div
                key={category.id}
                className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <FolderIcon className="h-6 w-6 text-blue-600 flex-shrink-0" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      {category.name}
                    </h3>
                  </div>
                  {category.color && (
                    <div 
                      className="w-4 h-4 rounded-full border border-gray-200 flex-shrink-0"
                      style={{ backgroundColor: category.color }}
                    />
                  )}
                </div>
                
                {category.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {category.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {category.totalLinks || 0} {category.totalLinks === 1 ? 'Link' : 'Links'}
                  </span>
                  
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/category/${category.slug}`}
                      className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Anzeigen
                    </Link>
                    <button
                      onClick={() => handleDeleteCategory(category.id, category.name)}
                      className="p-1 text-red-600 hover:text-red-700 transition-colors"
                      title="Kategorie löschen"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FolderIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Noch keine Kategorien erstellt
            </h3>
            <p className="text-gray-600 mb-6">
              Erstelle deine erste Kategorie, um Links zu organisieren.
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <FolderPlusIcon className="h-5 w-5 mr-2" />
              Erste Kategorie erstellen
            </button>
          </div>
        )}

        {/* Create Category Modal */}
        <CreateCategoryModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleCategoryCreated}
        />
      </div>
    </div>
  );
}
