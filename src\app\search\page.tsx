'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSearch } from '@/hooks/useSearch';
import { useCategories } from '@/hooks/useCategories';
import LinkCard from '@/components/UI/LinkCard';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ClockIcon,
  XMarkIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

function SearchContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialQuery = searchParams.get('q') || '';
  
  const [searchInput, setSearchInput] = useState(initialQuery);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'rating' | 'popular'>('newest');

  const { categories } = useCategories();
  const {
    results,
    loading,
    error,
    hasMore,
    totalCount,
    query,
    search,
    loadMore,
    clearSearch,
    searchWithFilters,
    searchHistory,
    isSearching
  } = useSearch({
    sortBy,
    categoryId: selectedCategory || undefined,
    pageSize: 15
  });

  // Perform initial search if query param exists
  useEffect(() => {
    if (initialQuery) {
      search(initialQuery, true);
    }
  }, [initialQuery, search]);

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      // Update URL
      const params = new URLSearchParams();
      params.set('q', searchQuery.trim());
      if (selectedCategory) params.set('category', selectedCategory);
      if (sortBy !== 'newest') params.set('sort', sortBy);
      
      router.push(`/search?${params.toString()}`);
      
      // Perform search
      searchWithFilters(searchQuery.trim(), {
        sortBy,
        categoryId: selectedCategory || undefined
      });
    }
  };

  const handleFilterChange = () => {
    if (query) {
      searchWithFilters(query, {
        sortBy,
        categoryId: selectedCategory || undefined
      });
    }
  };

  const handleHistoryClick = (historyQuery: string) => {
    setSearchInput(historyQuery);
    handleSearch(historyQuery);
  };

  const handleClearHistory = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('deeplinked_search_history');
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Suche</h1>
          <p className="text-gray-600">
            Durchsuche alle Links nach Titel, Beschreibung, Tags und Domain
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Links durchsuchen..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch(searchInput);
                }
              }}
              className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
            />
            <div className="absolute inset-y-0 right-0 flex items-center">
              {searchInput && (
                <button
                  onClick={() => {
                    setSearchInput('');
                    clearSearch();
                    router.push('/search');
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              )}
              <button
                onClick={() => handleSearch(searchInput)}
                disabled={!searchInput.trim() || isSearching}
                className="mr-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSearching ? (
                  <ArrowPathIcon className="h-4 w-4 animate-spin" />
                ) : (
                  'Suchen'
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Search History */}
        {!query && searchHistory.length > 0 && (
          <div className="mb-6 bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-900 flex items-center">
                <ClockIcon className="h-4 w-4 mr-2" />
                Letzte Suchen
              </h3>
              <button
                onClick={handleClearHistory}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Löschen
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {searchHistory.map((historyQuery, index) => (
                <button
                  key={index}
                  onClick={() => handleHistoryClick(historyQuery)}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                >
                  {historyQuery}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filter
            {(selectedCategory || sortBy !== 'newest') && (
              <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                Aktiv
              </span>
            )}
          </button>

          {showFilters && (
            <div className="mt-4 bg-white rounded-lg border border-gray-200 p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Kategorie
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => {
                      setSelectedCategory(e.target.value);
                      setTimeout(handleFilterChange, 100);
                    }}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Alle Kategorien</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Sort Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sortieren nach
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => {
                      setSortBy(e.target.value as typeof sortBy);
                      setTimeout(handleFilterChange, 100);
                    }}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="newest">Neueste zuerst</option>
                    <option value="oldest">Älteste zuerst</option>
                    <option value="rating">Beste Bewertung</option>
                    <option value="popular">Beliebteste</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results */}
        <div>
          {/* Results Header */}
          {query && (
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900">
                Suchergebnisse für &quot;{query}&quot;
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {totalCount} {totalCount === 1 ? 'Ergebnis' : 'Ergebnisse'} gefunden
              </p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="text-red-800">
                <strong>Fehler:</strong> {error}
              </div>
            </div>
          )}

          {/* Results List */}
          {loading && results.length === 0 ? (
            // Loading skeleton
            <div className="space-y-6">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : results.length > 0 ? (
            <div className="space-y-6">
              {results.map((link) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={true}
                />
              ))}
              
              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-6">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    {loading ? 'Lädt...' : 'Mehr Ergebnisse laden'}
                  </button>
                </div>
              )}
            </div>
          ) : query ? (
            // No results
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Keine Ergebnisse gefunden
              </h3>
              <p className="text-gray-600 mb-6">
                Keine Links gefunden für &quot;{query}&quot;. Versuche andere Suchbegriffe oder entferne Filter.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  onClick={() => {
                    setSelectedCategory('');
                    setSortBy('newest');
                    handleFilterChange();
                  }}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Filter zurücksetzen
                </button>
                <Link
                  href="/links"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  Alle Links anzeigen
                </Link>
              </div>
            </div>
          ) : (
            // Initial state
            <div className="text-center py-12">
              <MagnifyingGlassIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Bereit zum Suchen?
              </h3>
              <p className="text-gray-600">
                Gib einen Suchbegriff ein, um Links zu finden.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Suche wird geladen...</p>
        </div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}
