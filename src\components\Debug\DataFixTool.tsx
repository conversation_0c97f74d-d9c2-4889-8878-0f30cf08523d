'use client';

import { useState } from 'react';
import { linkService } from '@/services/linkService';
import { userService } from '@/services/userService';
import { updateDoc, doc } from 'firebase/firestore';
import { db } from '@/firebase/config';

export default function DataFixTool() {
  const [results, setResults] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(false);

  const fixPasiLinks = async () => {
    setLoading(true);
    try {
      console.log('🔧 Starting data fix...');
      
      // Get current pasi user
      const currentPasiUser = await userService.getByUsername('pasi91');
      if (!currentPasiUser) {
        setResults({ error: 'Current pasi user not found' });
        return;
      }
      
      // Get all links
      const allLinksResponse = await linkService.getAll({ pageSize: 200 });
      const allLinks = allLinksResponse.data;
      
      // Find pasi's links with old ID
      const pasiLinksWithOldId = allLinks.filter((link: Record<string, unknown>) =>
        link.submitter?.username === 'pasi91' && 
        link.submittedBy !== currentPasiUser.id
      );
      
      console.log('🔍 Found pasi links with old ID:', pasiLinksWithOldId);
      
      // Update each link's submittedBy field
      const updatePromises = pasiLinksWithOldId.map(async (link: Record<string, unknown>) => {
        console.log(`📝 Updating link "${link.title}" from ${link.submittedBy} to ${currentPasiUser.id}`);
        
        await updateDoc(doc(db, 'links', link.id), {
          submittedBy: currentPasiUser.id
        });
        
        return {
          id: link.id,
          title: link.title,
          oldId: link.submittedBy,
          newId: currentPasiUser.id
        };
      });
      
      const updateResults = await Promise.all(updatePromises);
      
      // Verify the fix
      const verificationLinks = await linkService.getByUserId(currentPasiUser.id, true);
      
      setResults({
        success: true,
        currentPasiId: currentPasiUser.id,
        linksFound: pasiLinksWithOldId.length,
        linksUpdated: updateResults.length,
        updateResults,
        verificationLinksCount: verificationLinks.length,
        verificationLinks: verificationLinks.map(l => ({
          id: l.id,
          title: l.title,
          submittedBy: l.submittedBy,
          isApproved: l.isApproved
        }))
      });
      
    } catch (error) {
      console.error('🚨 Data fix error:', error);
      setResults({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-red-900 text-white rounded-lg mb-4">
      <h3 className="text-lg font-bold mb-4">🔧 Data Fix Tool</h3>
      
      <div className="mb-4 text-sm">
        <p><strong>Problem:</strong> pasi&apos;s links have old User-ID</p>
        <p><strong>Solution:</strong> Update submittedBy field to current pasi ID</p>
        <p><strong>Links to fix:</strong> 3 (Fußballlogos, Paris vs Inter, Money making)</p>
      </div>
      
      <button
        onClick={fixPasiLinks}
        disabled={loading}
        className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
      >
        {loading ? '🔄 Fixing...' : '🚀 Fix pasi&apos;s Links'}
      </button>

      {results && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Results:</h4>
          <pre className="bg-black p-3 rounded text-green-400 text-sm overflow-auto max-h-80">
            {JSON.stringify(results, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 