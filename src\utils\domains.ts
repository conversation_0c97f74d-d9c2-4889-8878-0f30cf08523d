/**
 * Utility functions for domain-based logic
 */

/**
 * List of trusted domains that should be auto-approved
 */
export const TRUSTED_DOMAINS = [
  'youtube.com',
  'www.youtube.com',
  'youtu.be',
  'm.youtube.com'
] as const;

/**
 * Check if a URL is from a trusted domain that should be auto-approved
 * @param url - The URL to check
 * @returns boolean indicating if the domain is trusted
 */
export const isTrustedDomain = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    return TRUSTED_DOMAINS.some(trustedDomain => 
      hostname === trustedDomain || hostname.endsWith(`.${trustedDomain}`)
    );
  } catch {
    // Invalid URL
    return false;
  }
};

/**
 * Check if a URL is from YouTube
 * @param url - The URL to check
 * @returns boolean indicating if it's a YouTube URL
 */
export const isYouTubeUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    return hostname === 'youtube.com' || 
           hostname === 'www.youtube.com' || 
           hostname === 'youtu.be' || 
           hostname === 'm.youtube.com';
  } catch {
    // Invalid URL
    return false;
  }
};

/**
 * Get the domain category for a URL (for future use)
 * @param url - The URL to categorize
 * @returns string indicating the domain category
 */
export const getDomainCategory = (url: string): string => {
  if (isYouTubeUrl(url)) {
    return 'video';
  }
  
  // Add more domain categories as needed
  return 'general';
};
